# MomentumTsunami API Reference

## Core Classes and Methods

### MomentumTsunamiBot

Main trading bot class that orchestrates all components.

```python
from src.main import MomentumTsunamiBot
from config import load_config

# Initialize bot
config = load_config()
bot = MomentumTsunamiBot(config)

# Start trading
await bot.start()
```

#### Methods

##### `__init__(config: Config)`
Initialize the trading bot with configuration.

**Parameters:**
- `config`: Configuration object

##### `async start()`
Start the trading bot main loop.

##### `async shutdown()`
Gracefully shutdown the bot.

---

### DataFeed

Manages multiple data providers with failover capabilities.

```python
from src.data.data_feed import DataFeed

data_feed = DataFeed(config)
await data_feed.initialize()
```

#### Methods

##### `async get_real_time_data(symbol: str) -> Dict[str, Any]`
Get real-time quote data with caching and failover.

**Parameters:**
- `symbol`: Stock symbol (e.g., "AAPL")

**Returns:**
```python
{
    'symbol': 'AAPL',
    'price': 150.25,
    'change': 2.50,
    'change_percent': '1.69%',
    'volume': 45000000,
    'previous_close': 147.75,
    'timestamp': '2023-12-01T15:30:00'
}
```

##### `async get_historical_data(symbol: str, period: str = "1y") -> pd.DataFrame`
Get historical OHLCV data.

**Parameters:**
- `symbol`: Stock symbol
- `period`: Time period ("1y", "6m", "3m", "1m")

**Returns:** DataFrame with columns: open, high, low, close, volume

##### `async get_multiple_quotes(symbols: List[str]) -> Dict[str, Dict[str, Any]]`
Get quotes for multiple symbols concurrently.

---

### GapScanner

Identifies stocks gapping up based on momentum criteria.

```python
from src.data.gap_scanner import GapScanner

scanner = GapScanner(config, data_feed)
await scanner.initialize()
```

#### Methods

##### `async scan_gap_ups() -> List[Dict[str, Any]]`
Scan for stocks gapping up based on criteria.

**Returns:**
```python
[
    {
        'symbol': 'AAPL',
        'current_price': 105.0,
        'previous_close': 100.0,
        'gap_percent': 5.0,
        'volume': 2000000,
        'avg_volume': 1000000,
        'volume_ratio': 2.0,
        'gap_fill_level': 100.0,
        'scan_time': '2023-12-01T09:45:00'
    }
]
```

##### `async filter_stocks(gap_stocks: List[Dict[str, Any]]) -> List[Dict[str, Any]]`
Apply additional filters to gap-up stocks.

---

### MomentumStrategy

Main trading strategy implementation.

```python
from src.strategy.strategy import MomentumStrategy

strategy = MomentumStrategy(config)
```

#### Methods

##### `async analyze_stock(stock_data: Dict[str, Any], data_feed) -> Dict[str, Any]`
Analyze a gap-up stock for trading opportunities.

**Parameters:**
- `stock_data`: Gap stock data from scanner
- `data_feed`: Data feed instance

**Returns:**
```python
{
    'symbol': 'AAPL',
    'analysis_status': 'complete',
    'current_price': 105.0,
    'gap_percent': 5.0,
    'recommendation': 'BUY',
    'reason': 'All entry conditions met',
    'entry_score': 0.85
}
```

##### `async check_entry_conditions(stock_data: Dict[str, Any], market_data: pd.DataFrame) -> Optional[Dict[str, Any]]`
Check if entry conditions are met.

**Returns:**
```python
{
    'symbol': 'AAPL',
    'signal_type': 'ENTRY_LONG',
    'price': 104.50,
    'stop_loss': 102.00,
    'profit_target': 107.75,
    'risk_reward_ratio': 1.5,
    'entry_score': 0.85
}
```

---

### RiskManager

Comprehensive risk management system.

```python
from src.risk.risk_manager import RiskManager

risk_manager = RiskManager(config)
```

#### Methods

##### `calculate_position_size(account_size: float, risk_percent: float, entry_price: float, stop_loss: float) -> int`
Calculate position size based on risk management rules.

**Parameters:**
- `account_size`: Total account size
- `risk_percent`: Risk percentage per trade
- `entry_price`: Entry price per share
- `stop_loss`: Stop loss price per share

**Returns:** Number of shares to buy

##### `validate_new_position(symbol: str, entry_price: float, stop_loss: float, quantity: int) -> Dict[str, Any]`
Validate if a new position can be opened.

**Returns:**
```python
{
    'approved': True,
    'reason': 'Position approved',
    'adjusted_quantity': 100,
    'risk_amount': 200.0,
    'position_value': 10000.0
}
```

##### `add_position(symbol: str, entry_price: float, quantity: int, stop_loss: float) -> bool`
Add a new position to risk tracking.

##### `close_position(symbol: str, exit_price: float) -> Optional[Dict[str, Any]]`
Close a position and update P&L tracking.

---

### OrderManager

Manages order execution across different brokers.

```python
from src.execution.order_manager import OrderManager

order_manager = OrderManager(config)
await order_manager.initialize()
```

#### Methods

##### `async place_order(symbol: str, side: str, quantity: int, order_type: str = 'market') -> Optional[Dict[str, Any]]`
Place an order with retry logic and validation.

**Parameters:**
- `symbol`: Stock symbol
- `side`: 'buy' or 'sell'
- `quantity`: Number of shares
- `order_type`: 'market', 'limit', 'stop', 'stop_limit'

**Returns:**
```python
{
    'order_id': 'ORDER_123456',
    'symbol': 'AAPL',
    'side': 'buy',
    'quantity': 100,
    'status': 'filled',
    'fill_price': 104.50
}
```

##### `async cancel_order(order_id: str) -> bool`
Cancel an order.

##### `async get_positions() -> Dict[str, Any]`
Get current positions from broker.

---

### PerformanceTracker

Tracks and analyzes trading performance.

```python
from src.analytics.performance_tracker import PerformanceTracker

tracker = PerformanceTracker(config)
```

#### Methods

##### `async record_trade(trade_data: Dict[str, Any]) -> bool`
Record a completed trade.

**Parameters:**
```python
trade_data = {
    'symbol': 'AAPL',
    'entry_price': 100.0,
    'exit_price': 105.0,
    'quantity': 100,
    'entry_time': datetime.now(),
    'exit_time': datetime.now(),
    'pnl': 500.0,
    'exit_reason': 'profit_target'
}
```

##### `calculate_performance_metrics(trades: List[TradeRecord] = None) -> Dict[str, Any]`
Calculate comprehensive performance metrics.

**Returns:**
```python
{
    'total_trades': 50,
    'winning_trades': 30,
    'losing_trades': 20,
    'win_rate': 60.0,
    'total_pnl': 2500.0,
    'avg_win': 150.0,
    'avg_loss': -75.0,
    'profit_factor': 2.0,
    'sharpe_ratio': 1.5,
    'max_drawdown': 500.0
}
```

---

### TechnicalIndicators

Technical analysis indicators calculator.

```python
from src.strategy.indicators import TechnicalIndicators

indicators = TechnicalIndicators(config)
```

#### Methods

##### `calculate_ema(prices: pd.Series, period: int) -> pd.Series`
Calculate Exponential Moving Average.

##### `calculate_rsi(prices: pd.Series, period: int = 14) -> pd.Series`
Calculate Relative Strength Index.

##### `calculate_atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series`
Calculate Average True Range.

##### `calculate_momentum_indicators(data: pd.DataFrame) -> Dict[str, pd.Series]`
Calculate multiple momentum indicators.

**Returns:**
```python
{
    'ema_20': pd.Series,
    'ema_50': pd.Series,
    'rsi': pd.Series,
    'atr': pd.Series,
    'macd': pd.Series,
    'volume_sma': pd.Series
}
```

---

### Backtester

Backtesting framework for strategy validation.

```python
from src.analytics.backtester import Backtester

backtester = Backtester(config)
```

#### Methods

##### `async run_single_backtest(start_date: str, end_date: str) -> Dict[str, Any]`
Run a single backtest over specified date range.

**Returns:**
```python
{
    'start_date': '2023-01-01',
    'end_date': '2023-12-31',
    'initial_capital': 25000.0,
    'final_equity': 27500.0,
    'total_trades': 150,
    'statistics': {
        'total_return_percent': 10.0,
        'win_rate': 65.0,
        'sharpe_ratio': 1.8,
        'max_drawdown': 1200.0
    }
}
```

---

## Configuration Classes

### Config

Main configuration class with all settings.

```python
from config import Config, load_config

# Load from file
config = load_config('config/settings.yaml')

# Access settings
print(config.trading.risk_per_trade_percent)
print(config.account.account_size)
```

#### Properties

- `api`: API configuration (keys, endpoints)
- `trading`: Trading strategy parameters
- `account`: Account and broker settings
- `data`: Data provider configuration
- `logging`: Logging configuration

---

## Utility Functions

### Helper Functions

```python
from src.utils.helpers import *

# Market timing
is_market_open = is_market_hours()
is_premarket = is_pre_market()

# Calculations
gap_pct = calculate_gap_percentage(current_price, previous_close)
position_size = calculate_position_size(account_size, risk_pct, entry, stop)
profit_target = calculate_profit_target(entry, stop, ratio)

# Formatting
formatted_currency = format_currency(1234.56)  # "$1,234.56"
formatted_percent = format_percentage(5.25)    # "5.25%"
```

### Database Functions

```python
from src.utils.database import DatabaseManager

db = DatabaseManager(config)

# Save data
db.save_position(position_data)
db.save_order(order_data)
db.save_gap_scan(scan_data)

# Retrieve data
positions = db.load_positions()
stats = db.get_trading_stats(days=30)
```

---

## Error Handling

### Common Exceptions

```python
from src.exceptions import *

try:
    await data_feed.get_real_time_data("INVALID")
except DataProviderError as e:
    logger.error(f"Data provider error: {e}")

try:
    risk_manager.validate_new_position(...)
except RiskViolationError as e:
    logger.warning(f"Risk violation: {e}")
```

### Retry Decorators

```python
from src.utils.helpers import retry_async

@retry_async(max_retries=3, delay=1.0)
async def api_call():
    # Your API call here
    pass
```

---

## Event System

### Signal Types

```python
# Entry signals
{
    'signal_type': 'ENTRY_LONG',
    'symbol': 'AAPL',
    'price': 104.50,
    'stop_loss': 102.00,
    'profit_target': 107.75
}

# Exit signals
{
    'signal_type': 'EXIT_LONG',
    'symbol': 'AAPL',
    'price': 106.00,
    'reason': 'profit_target',
    'pnl': 150.0
}
```

---

## Testing Framework

### Unit Tests

```python
import pytest
from tests.test_strategy import TestMomentumStrategy

# Run specific test
pytest tests/test_strategy.py::TestMomentumStrategy::test_entry_conditions

# Run all tests
pytest tests/ -v
```

### Mock Objects

```python
from tests.mocks import MockDataFeed, MockBroker

# Use in tests
mock_feed = MockDataFeed(config)
mock_broker = MockBroker(config)
```

---

## Integration Examples

### Basic Trading Bot

```python
import asyncio
from config import load_config
from src.main import MomentumTsunamiBot

async def main():
    config = load_config()
    config.mode = "paper"
    
    bot = MomentumTsunamiBot(config)
    await bot.start()

if __name__ == "__main__":
    asyncio.run(main())
```

### Custom Strategy

```python
from src.strategy.strategy import MomentumStrategy

class CustomStrategy(MomentumStrategy):
    async def analyze_stock(self, stock_data, data_feed):
        # Your custom analysis logic
        analysis = await super().analyze_stock(stock_data, data_feed)
        
        # Add custom criteria
        if analysis['recommendation'] == 'BUY':
            # Additional checks
            pass
            
        return analysis
```

### Performance Analysis

```python
from src.analytics.performance_tracker import PerformanceTracker

tracker = PerformanceTracker(config)

# Get performance metrics
metrics = tracker.calculate_performance_metrics()
print(f"Win Rate: {metrics['win_rate']:.1f}%")
print(f"Profit Factor: {metrics['profit_factor']:.2f}")

# Export trades
tracker.export_trades_to_csv("trades_2023.csv")
```
