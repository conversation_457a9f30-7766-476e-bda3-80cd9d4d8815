#!/usr/bin/env python3
"""
Tests for MomentumTsunami data feed system.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from config import Config
from data.data_feed import DataFeed
from data.gap_scanner import GapScanner
from data.market_data import YFinanceProvider


class MockDataProvider:
    """Mock data provider for testing."""
    
    def __init__(self, config):
        self.config = config
        self.should_fail = False
    
    async def initialize(self):
        pass
    
    async def cleanup(self):
        pass
    
    async def get_quote(self, symbol: str):
        if self.should_fail:
            raise Exception("Mock provider failure")
        
        return {
            'symbol': symbol,
            'price': 100.0 + hash(symbol) % 50,
            'change': 2.5,
            'change_percent': '2.5%',
            'volume': 1000000,
            'previous_close': 97.5,
            'timestamp': datetime.now().isoformat()
        }
    
    async def get_historical_data(self, symbol: str, period: str = "1y"):
        if self.should_fail:
            return pd.DataFrame()
        
        # Generate mock historical data
        dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
        np.random.seed(hash(symbol) % 2**32)
        
        base_price = 100.0
        data = []
        
        for date in dates:
            daily_return = np.random.normal(0.001, 0.02)
            base_price *= (1 + daily_return)
            
            data.append({
                'open': base_price * 0.99,
                'high': base_price * 1.02,
                'low': base_price * 0.98,
                'close': base_price,
                'volume': int(np.random.uniform(500000, 2000000))
            })
        
        df = pd.DataFrame(data, index=dates)
        return df
    
    async def get_intraday_data(self, symbol: str, interval: str = "1min"):
        if self.should_fail:
            return pd.DataFrame()
        
        # Generate mock intraday data
        dates = pd.date_range(start='2023-01-01 09:30', periods=50, freq='5min')
        np.random.seed(hash(symbol) % 2**32)
        
        base_price = 100.0
        data = []
        
        for date in dates:
            minute_return = np.random.normal(0, 0.001)
            base_price *= (1 + minute_return)
            
            data.append({
                'open': base_price * 0.999,
                'high': base_price * 1.001,
                'low': base_price * 0.999,
                'close': base_price,
                'volume': int(np.random.uniform(1000, 10000))
            })
        
        df = pd.DataFrame(data, index=dates)
        return df
    
    async def search_symbols(self, query: str):
        return [
            {
                'symbol': f'{query}1',
                'name': f'{query} Test Company 1',
                'type': 'Common Stock',
                'region': 'US',
                'currency': 'USD'
            }
        ]


class TestDataFeed:
    """Test data feed functionality."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        config = Config()
        config.logging.level = "ERROR"
        config.data.cache_enabled = True
        config.data.primary_provider = "mock"
        config.data.backup_providers = ["mock2"]
        return config
    
    @pytest.fixture
    def data_feed(self, config):
        """Create data feed with mock providers."""
        data_feed = DataFeed(config)
        
        # Replace providers with mock providers
        data_feed.providers = {
            'mock': MockDataProvider(config),
            'mock2': MockDataProvider(config)
        }
        data_feed.primary_provider = data_feed.providers['mock']
        data_feed.backup_providers = [data_feed.providers['mock2']]
        
        return data_feed
    
    @pytest.mark.asyncio
    async def test_initialization(self, data_feed):
        """Test data feed initialization."""
        await data_feed.initialize()
        
        # Should complete without errors
        assert data_feed.primary_provider is not None
        assert len(data_feed.backup_providers) > 0
    
    @pytest.mark.asyncio
    async def test_get_real_time_data(self, data_feed):
        """Test real-time data retrieval."""
        await data_feed.initialize()
        
        quote = await data_feed.get_real_time_data("AAPL")
        
        assert quote is not None
        assert 'symbol' in quote
        assert 'price' in quote
        assert 'volume' in quote
        assert quote['symbol'] == "AAPL"
    
    @pytest.mark.asyncio
    async def test_get_real_time_data_with_failover(self, data_feed):
        """Test real-time data with failover to backup provider."""
        await data_feed.initialize()
        
        # Make primary provider fail
        data_feed.primary_provider.should_fail = True
        
        quote = await data_feed.get_real_time_data("AAPL")
        
        # Should still get data from backup provider
        assert quote is not None
        assert 'symbol' in quote
    
    @pytest.mark.asyncio
    async def test_get_historical_data(self, data_feed):
        """Test historical data retrieval."""
        await data_feed.initialize()
        
        data = await data_feed.get_historical_data("AAPL", "1y")
        
        assert not data.empty
        assert 'open' in data.columns
        assert 'high' in data.columns
        assert 'low' in data.columns
        assert 'close' in data.columns
        assert 'volume' in data.columns
    
    @pytest.mark.asyncio
    async def test_get_intraday_data(self, data_feed):
        """Test intraday data retrieval."""
        await data_feed.initialize()
        
        data = await data_feed.get_intraday_data("AAPL", "5min")
        
        assert not data.empty
        assert 'open' in data.columns
        assert 'close' in data.columns
        assert 'volume' in data.columns
    
    @pytest.mark.asyncio
    async def test_get_multiple_quotes(self, data_feed):
        """Test multiple quotes retrieval."""
        await data_feed.initialize()
        
        symbols = ["AAPL", "MSFT", "GOOGL"]
        quotes = await data_feed.get_multiple_quotes(symbols)
        
        assert len(quotes) == 3
        for symbol in symbols:
            assert symbol in quotes
            assert quotes[symbol] is not None
    
    @pytest.mark.asyncio
    async def test_search_symbols(self, data_feed):
        """Test symbol search."""
        await data_feed.initialize()
        
        results = await data_feed.search_symbols("AAPL")
        
        assert isinstance(results, list)
        if results:  # Mock provider returns results
            assert 'symbol' in results[0]
            assert 'name' in results[0]
    
    def test_cache_functionality(self, data_feed):
        """Test caching functionality."""
        # Test cache validation
        assert not data_feed._is_cache_valid("AAPL", "quote")
        
        # Add to cache
        data_feed.quote_cache["AAPL"] = {"price": 150.0}
        data_feed.cache_timestamps["AAPL_quote"] = datetime.now()
        
        # Should be valid now
        assert data_feed._is_cache_valid("AAPL", "quote")
        
        # Clear cache
        data_feed.clear_cache()
        assert not data_feed._is_cache_valid("AAPL", "quote")
    
    @pytest.mark.asyncio
    async def test_health_check(self, data_feed):
        """Test health check functionality."""
        await data_feed.initialize()
        
        health_status = await data_feed.health_check()
        
        assert 'timestamp' in health_status
        assert 'providers' in health_status
        assert len(health_status['providers']) > 0
        
        for provider_name, status in health_status['providers'].items():
            assert 'status' in status
            assert 'last_check' in status
    
    @pytest.mark.asyncio
    async def test_cleanup(self, data_feed):
        """Test cleanup functionality."""
        await data_feed.initialize()
        await data_feed.cleanup()
        
        # Should complete without errors


class TestGapScanner:
    """Test gap scanner functionality."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        config = Config()
        config.logging.level = "ERROR"
        config.trading.min_gap_percent = 3.0
        config.trading.max_gap_percent = 8.0
        config.trading.min_price = 10.0
        config.trading.max_price = 200.0
        config.trading.min_volume_multiplier = 2.0
        config.debug = True  # Allow scanning outside pre-market
        return config
    
    @pytest.fixture
    def mock_data_feed(self, config):
        """Create mock data feed."""
        data_feed = DataFeed(config)
        data_feed.providers = {'mock': MockDataProvider(config)}
        data_feed.primary_provider = data_feed.providers['mock']
        return data_feed
    
    @pytest.fixture
    def gap_scanner(self, config, mock_data_feed):
        """Create gap scanner with mock data feed."""
        return GapScanner(config, mock_data_feed)
    
    @pytest.mark.asyncio
    async def test_initialization(self, gap_scanner):
        """Test gap scanner initialization."""
        await gap_scanner.initialize()
        
        assert len(gap_scanner.stock_universe) > 0
        assert 'AAPL' in gap_scanner.stock_universe
    
    @pytest.mark.asyncio
    async def test_gap_scan(self, gap_scanner):
        """Test gap scanning functionality."""
        await gap_scanner.initialize()
        
        # Mock the data feed to return gap-up data
        async def mock_get_real_time_data(symbol):
            return {
                'symbol': symbol,
                'price': 105.0,  # 5% gap up from 100
                'previous_close': 100.0,
                'volume': 2000000
            }
        
        gap_scanner.data_feed.get_real_time_data = mock_get_real_time_data
        
        gap_stocks = await gap_scanner.scan_gap_ups()
        
        # Should find gap-up stocks (mocked data shows 5% gap)
        assert isinstance(gap_stocks, list)
    
    @pytest.mark.asyncio
    async def test_stock_filtering(self, gap_scanner):
        """Test stock filtering functionality."""
        await gap_scanner.initialize()
        
        # Create mock gap stocks
        gap_stocks = [
            {
                'symbol': 'TEST1',
                'gap_percent': 5.0,
                'current_price': 105.0,
                'volume': 2000000,
                'avg_volume': 1000000,
                'volume_ratio': 2.0,
                'market_cap': 10000000000,
                'float_shares': 100000000
            },
            {
                'symbol': 'TEST2',
                'gap_percent': 4.0,
                'current_price': 52.0,
                'volume': 1500000,
                'avg_volume': 1000000,
                'volume_ratio': 1.5,
                'market_cap': 5000000000,
                'float_shares': 50000000
            }
        ]
        
        filtered_stocks = await gap_scanner.filter_stocks(gap_stocks)
        
        assert isinstance(filtered_stocks, list)
        # Should have gap scores calculated
        for stock in filtered_stocks:
            assert 'gap_score' in stock
            assert 0 <= stock['gap_score'] <= 1
    
    def test_gap_score_calculation(self, gap_scanner):
        """Test gap score calculation."""
        stock = {
            'gap_percent': 5.0,
            'volume_ratio': 3.0,
            'current_price': 50.0,
            'market_cap': 10000000000,
            'has_news_catalyst': True
        }
        
        score = gap_scanner._calculate_gap_score(stock)
        
        assert 0 <= score <= 1
        assert score > 0  # Should have positive score with good metrics
    
    def test_scan_summary(self, gap_scanner):
        """Test scan summary generation."""
        summary = gap_scanner.get_scan_summary()
        
        assert 'universe_size' in summary
        assert 'scan_criteria' in summary
        assert summary['universe_size'] > 0


class TestYFinanceProvider:
    """Test Yahoo Finance provider."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        config = Config()
        config.logging.level = "ERROR"
        return config
    
    @pytest.fixture
    def provider(self, config):
        """Create YFinance provider."""
        return YFinanceProvider(config)
    
    @pytest.mark.asyncio
    async def test_initialization(self, provider):
        """Test provider initialization."""
        await provider.initialize()
        await provider.cleanup()
        # Should complete without errors
    
    @pytest.mark.asyncio
    async def test_get_quote(self, provider):
        """Test quote retrieval."""
        # This test might fail if no internet connection
        try:
            quote = await provider.get_quote("AAPL")
            
            if quote:  # Only test if we got data
                assert 'symbol' in quote
                assert 'price' in quote
                assert quote['symbol'] == "AAPL"
        except Exception:
            # Skip test if network issues
            pytest.skip("Network connection required for YFinance test")
    
    @pytest.mark.asyncio
    async def test_get_historical_data(self, provider):
        """Test historical data retrieval."""
        try:
            data = await provider.get_historical_data("AAPL", "1y")
            
            if not data.empty:  # Only test if we got data
                assert 'open' in data.columns
                assert 'close' in data.columns
                assert len(data) > 0
        except Exception:
            # Skip test if network issues
            pytest.skip("Network connection required for YFinance test")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
