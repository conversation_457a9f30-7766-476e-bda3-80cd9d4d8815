#!/usr/bin/env python3
"""
Market data provider interfaces for MomentumTsunami trading bot.
"""

import asyncio
import aiohttp
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import pandas as pd
from alpha_vantage.timeseries import TimeSeries
from alpha_vantage.fundamentaldata import FundamentalData
import yfinance as yf

from utils.logger import setup_logger
from utils.helpers import retry_async


class MarketDataProvider(ABC):
    """Abstract base class for market data providers."""
    
    def __init__(self, config):
        self.config = config
        self.logger = setup_logger(f"MarketData.{self.__class__.__name__}", config.logging)
    
    @abstractmethod
    async def get_quote(self, symbol: str) -> Dict[str, Any]:
        """Get real-time quote for a symbol."""
        pass
    
    @abstractmethod
    async def get_historical_data(self, symbol: str, period: str = "1y") -> pd.DataFrame:
        """Get historical data for a symbol."""
        pass
    
    @abstractmethod
    async def get_intraday_data(self, symbol: str, interval: str = "1min") -> pd.DataFrame:
        """Get intraday data for a symbol."""
        pass
    
    @abstractmethod
    async def search_symbols(self, query: str) -> List[Dict[str, Any]]:
        """Search for symbols matching query."""
        pass


class AlphaVantageProvider(MarketDataProvider):
    """Alpha Vantage data provider."""
    
    def __init__(self, config):
        super().__init__(config)
        self.api_key = config.api.alpha_vantage_key
        self.ts = TimeSeries(key=self.api_key, output_format='pandas')
        self.fd = FundamentalData(key=self.api_key, output_format='pandas')
        self.base_url = "https://www.alphavantage.co/query"
        self.session = None
    
    async def initialize(self):
        """Initialize the provider."""
        self.session = aiohttp.ClientSession()
    
    async def cleanup(self):
        """Cleanup resources."""
        if self.session:
            await self.session.close()
    
    @retry_async(max_retries=3, delay=1.0)
    async def get_quote(self, symbol: str) -> Dict[str, Any]:
        """Get real-time quote from Alpha Vantage."""
        params = {
            'function': 'GLOBAL_QUOTE',
            'symbol': symbol,
            'apikey': self.api_key
        }
        
        async with self.session.get(self.base_url, params=params) as response:
            data = await response.json()
            
            if 'Global Quote' in data:
                quote = data['Global Quote']
                return {
                    'symbol': quote.get('01. symbol', symbol),
                    'price': float(quote.get('05. price', 0)),
                    'change': float(quote.get('09. change', 0)),
                    'change_percent': quote.get('10. change percent', '0%').replace('%', ''),
                    'volume': int(quote.get('06. volume', 0)),
                    'previous_close': float(quote.get('08. previous close', 0)),
                    'timestamp': quote.get('07. latest trading day', '')
                }
            else:
                self.logger.error(f"Error getting quote for {symbol}: {data}")
                return {}
    
    async def get_historical_data(self, symbol: str, period: str = "1y") -> pd.DataFrame:
        """Get historical daily data."""
        try:
            data, _ = self.ts.get_daily_adjusted(symbol=symbol, outputsize='full')
            
            # Rename columns
            data.columns = ['open', 'high', 'low', 'close', 'adjusted_close', 'volume', 'dividend', 'split']
            data.index.name = 'date'
            
            # Filter by period
            if period == "1y":
                cutoff_date = datetime.now() - timedelta(days=365)
                data = data[data.index >= cutoff_date]
            
            return data.sort_index()
            
        except Exception as e:
            self.logger.error(f"Error getting historical data for {symbol}: {e}")
            return pd.DataFrame()
    
    async def get_intraday_data(self, symbol: str, interval: str = "1min") -> pd.DataFrame:
        """Get intraday data."""
        try:
            data, _ = self.ts.get_intraday(symbol=symbol, interval=interval, outputsize='full')
            
            # Rename columns
            data.columns = ['open', 'high', 'low', 'close', 'volume']
            data.index.name = 'timestamp'
            
            return data.sort_index()
            
        except Exception as e:
            self.logger.error(f"Error getting intraday data for {symbol}: {e}")
            return pd.DataFrame()
    
    async def search_symbols(self, query: str) -> List[Dict[str, Any]]:
        """Search for symbols."""
        params = {
            'function': 'SYMBOL_SEARCH',
            'keywords': query,
            'apikey': self.api_key
        }
        
        async with self.session.get(self.base_url, params=params) as response:
            data = await response.json()
            
            if 'bestMatches' in data:
                return [
                    {
                        'symbol': match['1. symbol'],
                        'name': match['2. name'],
                        'type': match['3. type'],
                        'region': match['4. region'],
                        'currency': match['8. currency']
                    }
                    for match in data['bestMatches']
                ]
            
            return []


class PolygonProvider(MarketDataProvider):
    """Polygon.io data provider."""
    
    def __init__(self, config):
        super().__init__(config)
        self.api_key = config.api.polygon_key
        self.base_url = "https://api.polygon.io"
        self.session = None
    
    async def initialize(self):
        """Initialize the provider."""
        self.session = aiohttp.ClientSession()
    
    async def cleanup(self):
        """Cleanup resources."""
        if self.session:
            await self.session.close()
    
    @retry_async(max_retries=3, delay=1.0)
    async def get_quote(self, symbol: str) -> Dict[str, Any]:
        """Get real-time quote from Polygon."""
        url = f"{self.base_url}/v2/snapshot/locale/us/markets/stocks/tickers/{symbol}"
        params = {'apikey': self.api_key}
        
        async with self.session.get(url, params=params) as response:
            data = await response.json()
            
            if data.get('status') == 'OK' and 'ticker' in data:
                ticker = data['ticker']
                quote = ticker.get('lastQuote', {})
                day = ticker.get('day', {})
                prev_day = ticker.get('prevDay', {})
                
                return {
                    'symbol': ticker.get('ticker', symbol),
                    'price': day.get('c', 0),
                    'change': day.get('c', 0) - prev_day.get('c', 0),
                    'change_percent': ((day.get('c', 0) - prev_day.get('c', 0)) / prev_day.get('c', 1)) * 100,
                    'volume': day.get('v', 0),
                    'previous_close': prev_day.get('c', 0),
                    'timestamp': datetime.now().isoformat()
                }
            
            return {}
    
    async def get_historical_data(self, symbol: str, period: str = "1y") -> pd.DataFrame:
        """Get historical data from Polygon."""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365 if period == "1y" else 30)
        
        url = f"{self.base_url}/v2/aggs/ticker/{symbol}/range/1/day/{start_date.strftime('%Y-%m-%d')}/{end_date.strftime('%Y-%m-%d')}"
        params = {'apikey': self.api_key}
        
        async with self.session.get(url, params=params) as response:
            data = await response.json()
            
            if data.get('status') == 'OK' and 'results' in data:
                df_data = []
                for result in data['results']:
                    df_data.append({
                        'date': pd.to_datetime(result['t'], unit='ms'),
                        'open': result['o'],
                        'high': result['h'],
                        'low': result['l'],
                        'close': result['c'],
                        'volume': result['v']
                    })
                
                df = pd.DataFrame(df_data)
                df.set_index('date', inplace=True)
                return df.sort_index()
            
            return pd.DataFrame()
    
    async def get_intraday_data(self, symbol: str, interval: str = "1min") -> pd.DataFrame:
        """Get intraday data from Polygon."""
        # Convert interval to Polygon format
        multiplier = 1
        timespan = "minute"
        
        if interval == "5min":
            multiplier = 5
        elif interval == "15min":
            multiplier = 15
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=1)
        
        url = f"{self.base_url}/v2/aggs/ticker/{symbol}/range/{multiplier}/{timespan}/{start_date.strftime('%Y-%m-%d')}/{end_date.strftime('%Y-%m-%d')}"
        params = {'apikey': self.api_key}
        
        async with self.session.get(url, params=params) as response:
            data = await response.json()
            
            if data.get('status') == 'OK' and 'results' in data:
                df_data = []
                for result in data['results']:
                    df_data.append({
                        'timestamp': pd.to_datetime(result['t'], unit='ms'),
                        'open': result['o'],
                        'high': result['h'],
                        'low': result['l'],
                        'close': result['c'],
                        'volume': result['v']
                    })
                
                df = pd.DataFrame(df_data)
                df.set_index('timestamp', inplace=True)
                return df.sort_index()
            
            return pd.DataFrame()
    
    async def search_symbols(self, query: str) -> List[Dict[str, Any]]:
        """Search for symbols on Polygon."""
        url = f"{self.base_url}/v3/reference/tickers"
        params = {
            'search': query,
            'market': 'stocks',
            'active': 'true',
            'apikey': self.api_key
        }
        
        async with self.session.get(url, params=params) as response:
            data = await response.json()
            
            if data.get('status') == 'OK' and 'results' in data:
                return [
                    {
                        'symbol': result['ticker'],
                        'name': result.get('name', ''),
                        'type': result.get('type', ''),
                        'market': result.get('market', ''),
                        'currency': result.get('currency_name', 'USD')
                    }
                    for result in data['results']
                ]
            
            return []


class YFinanceProvider(MarketDataProvider):
    """Yahoo Finance data provider (backup/free option)."""
    
    def __init__(self, config):
        super().__init__(config)
    
    async def initialize(self):
        """Initialize the provider."""
        pass
    
    async def cleanup(self):
        """Cleanup resources."""
        pass
    
    async def get_quote(self, symbol: str) -> Dict[str, Any]:
        """Get quote from Yahoo Finance."""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            return {
                'symbol': symbol,
                'price': info.get('currentPrice', info.get('regularMarketPrice', 0)),
                'change': info.get('regularMarketChange', 0),
                'change_percent': info.get('regularMarketChangePercent', 0),
                'volume': info.get('regularMarketVolume', 0),
                'previous_close': info.get('previousClose', 0),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting quote for {symbol}: {e}")
            return {}
    
    async def get_historical_data(self, symbol: str, period: str = "1y") -> pd.DataFrame:
        """Get historical data from Yahoo Finance."""
        try:
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period)
            
            # Rename columns to match our format
            data.columns = [col.lower().replace(' ', '_') for col in data.columns]
            data.index.name = 'date'
            
            return data
            
        except Exception as e:
            self.logger.error(f"Error getting historical data for {symbol}: {e}")
            return pd.DataFrame()
    
    async def get_intraday_data(self, symbol: str, interval: str = "1min") -> pd.DataFrame:
        """Get intraday data from Yahoo Finance."""
        try:
            ticker = yf.Ticker(symbol)
            data = ticker.history(period="1d", interval=interval)
            
            data.columns = [col.lower().replace(' ', '_') for col in data.columns]
            data.index.name = 'timestamp'
            
            return data
            
        except Exception as e:
            self.logger.error(f"Error getting intraday data for {symbol}: {e}")
            return pd.DataFrame()
    
    async def search_symbols(self, query: str) -> List[Dict[str, Any]]:
        """Search symbols (limited functionality with yfinance)."""
        # yfinance doesn't have a search API, so this is a placeholder
        return []
