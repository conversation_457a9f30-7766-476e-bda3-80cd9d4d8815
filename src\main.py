#!/usr/bin/env python3
"""
MomentumTsunami - Main Trading Bot Entry Point

This is the main entry point for the MomentumTsunami day trading bot.
It orchestrates all components and manages the trading lifecycle.
"""

import asyncio
import argparse
import signal
import sys
from datetime import datetime, time
from typing import Optional
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from config import load_config, Config
from utils.logger import setup_logger
from data.gap_scanner import GapScanner
from data.data_feed import DataFeed
from strategy.strategy import MomentumStrategy
from risk.risk_manager import RiskManager
from execution.order_manager import OrderManager
from analytics.performance_tracker import PerformanceTracker


class MomentumTsunamiBot:
    """
    Main trading bot class that orchestrates all components.
    """
    
    def __init__(self, config: Config):
        """
        Initialize the trading bot.
        
        Args:
            config: Configuration object
        """
        self.config = config
        self.logger = setup_logger("MomentumTsunami", config.logging)
        self.running = False
        self.shutdown_event = asyncio.Event()
        
        # Initialize components
        self.data_feed = DataFeed(config)
        self.gap_scanner = GapScanner(config, self.data_feed)
        self.strategy = MomentumStrategy(config)
        self.risk_manager = RiskManager(config)
        self.order_manager = OrderManager(config)
        self.performance_tracker = PerformanceTracker(config)
        
        # Trading state
        self.watchlist = []
        self.positions = {}
        self.daily_pnl = 0.0
        self.trade_count = 0
        
        self.logger.info(f"MomentumTsunami Bot initialized in {config.mode} mode")
    
    async def start(self):
        """Start the trading bot."""
        self.logger.info("Starting MomentumTsunami Bot...")
        self.running = True
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        try:
            # Initialize all components
            await self._initialize_components()
            
            # Start main trading loop
            await self._run_trading_loop()
            
        except Exception as e:
            self.logger.error(f"Error in main trading loop: {e}")
            raise
        finally:
            await self.shutdown()
    
    async def _initialize_components(self):
        """Initialize all trading components."""
        self.logger.info("Initializing trading components...")
        
        # Initialize data feed
        await self.data_feed.initialize()
        
        # Initialize order manager
        await self.order_manager.initialize()
        
        # Load existing positions
        self.positions = await self.order_manager.get_positions()
        
        self.logger.info("All components initialized successfully")
    
    async def _run_trading_loop(self):
        """Main trading loop."""
        self.logger.info("Starting main trading loop...")
        
        while self.running and not self.shutdown_event.is_set():
            try:
                current_time = datetime.now().time()
                
                # Pre-market scanning (6:00 AM - 9:30 AM EST)
                if time(6, 0) <= current_time <= time(9, 30):
                    await self._pre_market_scan()
                
                # Market hours trading (9:30 AM - 4:00 PM EST)
                elif time(9, 30) <= current_time <= time(16, 0):
                    await self._market_hours_trading()
                
                # Post-market analysis (4:00 PM - 6:00 PM EST)
                elif time(16, 0) <= current_time <= time(18, 0):
                    await self._post_market_analysis()
                
                # Sleep between iterations
                await asyncio.sleep(self.config.data.update_interval_seconds)
                
            except Exception as e:
                self.logger.error(f"Error in trading loop: {e}")
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _pre_market_scan(self):
        """Perform pre-market gap scanning."""
        self.logger.debug("Performing pre-market scan...")
        
        try:
            # Scan for gap-up stocks
            gap_stocks = await self.gap_scanner.scan_gap_ups()
            
            # Filter stocks based on criteria
            filtered_stocks = await self.gap_scanner.filter_stocks(gap_stocks)
            
            # Update watchlist
            self.watchlist = filtered_stocks
            
            if self.watchlist:
                self.logger.info(f"Found {len(self.watchlist)} stocks for watchlist: {[s['symbol'] for s in self.watchlist]}")
            else:
                self.logger.info("No stocks found matching gap-up criteria")
                
        except Exception as e:
            self.logger.error(f"Error in pre-market scan: {e}")
    
    async def _market_hours_trading(self):
        """Handle market hours trading logic."""
        if not self.watchlist:
            return
        
        try:
            # Check daily loss limit
            if abs(self.daily_pnl) >= (self.config.account.account_size * self.config.trading.daily_loss_limit_percent / 100):
                self.logger.warning("Daily loss limit reached. Stopping trading for today.")
                return
            
            # Monitor existing positions
            await self._monitor_positions()
            
            # Look for new entry opportunities
            if len(self.positions) < self.config.trading.max_positions:
                await self._scan_for_entries()
                
        except Exception as e:
            self.logger.error(f"Error in market hours trading: {e}")
    
    async def _monitor_positions(self):
        """Monitor existing positions for exit signals."""
        for symbol, position in list(self.positions.items()):
            try:
                # Get current market data
                market_data = await self.data_feed.get_real_time_data(symbol)
                
                # Check exit conditions
                exit_signal = await self.strategy.check_exit_conditions(position, market_data)
                
                if exit_signal:
                    await self._close_position(symbol, exit_signal['reason'])
                    
            except Exception as e:
                self.logger.error(f"Error monitoring position {symbol}: {e}")
    
    async def _scan_for_entries(self):
        """Scan watchlist for entry opportunities."""
        for stock in self.watchlist:
            try:
                symbol = stock['symbol']
                
                # Skip if already have position
                if symbol in self.positions:
                    continue
                
                # Get current market data
                market_data = await self.data_feed.get_real_time_data(symbol)
                
                # Check entry conditions
                entry_signal = await self.strategy.check_entry_conditions(stock, market_data)
                
                if entry_signal:
                    await self._enter_position(symbol, entry_signal)
                    
            except Exception as e:
                self.logger.error(f"Error scanning {stock['symbol']} for entry: {e}")
    
    async def _enter_position(self, symbol: str, entry_signal: dict):
        """Enter a new position."""
        try:
            # Calculate position size
            position_size = self.risk_manager.calculate_position_size(
                account_size=self.config.account.account_size,
                risk_percent=self.config.trading.risk_per_trade_percent,
                entry_price=entry_signal['price'],
                stop_loss=entry_signal['stop_loss']
            )
            
            # Place order
            order = await self.order_manager.place_order(
                symbol=symbol,
                side='buy',
                quantity=position_size,
                order_type='market'
            )
            
            if order and order['status'] == 'filled':
                # Record position
                self.positions[symbol] = {
                    'symbol': symbol,
                    'quantity': position_size,
                    'entry_price': order['fill_price'],
                    'entry_time': datetime.now(),
                    'stop_loss': entry_signal['stop_loss'],
                    'profit_target': entry_signal['profit_target'],
                    'strategy': 'gap_momentum'
                }
                
                self.trade_count += 1
                self.logger.info(f"Entered position: {symbol} @ ${order['fill_price']:.2f} ({position_size} shares)")
                
        except Exception as e:
            self.logger.error(f"Error entering position {symbol}: {e}")
    
    async def _close_position(self, symbol: str, reason: str):
        """Close an existing position."""
        try:
            position = self.positions[symbol]
            
            # Place sell order
            order = await self.order_manager.place_order(
                symbol=symbol,
                side='sell',
                quantity=position['quantity'],
                order_type='market'
            )
            
            if order and order['status'] == 'filled':
                # Calculate P&L
                pnl = (order['fill_price'] - position['entry_price']) * position['quantity']
                self.daily_pnl += pnl
                
                # Record trade
                trade_record = {
                    'symbol': symbol,
                    'entry_price': position['entry_price'],
                    'exit_price': order['fill_price'],
                    'quantity': position['quantity'],
                    'pnl': pnl,
                    'entry_time': position['entry_time'],
                    'exit_time': datetime.now(),
                    'exit_reason': reason,
                    'strategy': position['strategy']
                }
                
                await self.performance_tracker.record_trade(trade_record)
                
                # Remove from positions
                del self.positions[symbol]
                
                self.logger.info(f"Closed position: {symbol} @ ${order['fill_price']:.2f} | P&L: ${pnl:.2f} | Reason: {reason}")
                
        except Exception as e:
            self.logger.error(f"Error closing position {symbol}: {e}")
    
    async def _post_market_analysis(self):
        """Perform post-market analysis."""
        try:
            # Generate daily performance report
            daily_stats = await self.performance_tracker.get_daily_stats()
            
            self.logger.info(f"Daily Summary - Trades: {self.trade_count}, P&L: ${self.daily_pnl:.2f}")
            
            # Reset daily counters
            self.trade_count = 0
            self.daily_pnl = 0.0
            self.watchlist = []
            
        except Exception as e:
            self.logger.error(f"Error in post-market analysis: {e}")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        self.logger.info(f"Received signal {signum}. Initiating graceful shutdown...")
        self.running = False
        self.shutdown_event.set()
    
    async def shutdown(self):
        """Gracefully shutdown the bot."""
        self.logger.info("Shutting down MomentumTsunami Bot...")
        
        try:
            # Close all positions if in paper trading mode
            if self.config.account.paper_trading:
                for symbol in list(self.positions.keys()):
                    await self._close_position(symbol, "shutdown")
            
            # Cleanup components
            await self.order_manager.cleanup()
            await self.data_feed.cleanup()
            
            self.logger.info("MomentumTsunami Bot shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")


async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="MomentumTsunami Day Trading Bot")
    parser.add_argument("--mode", choices=["paper", "live", "backtest"], default="paper",
                       help="Trading mode (default: paper)")
    parser.add_argument("--config", type=str, help="Path to configuration file")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    
    args = parser.parse_args()
    
    # Load configuration
    config = load_config(args.config)
    config.mode = args.mode
    config.debug = args.debug
    
    if args.debug:
        config.logging.level = "DEBUG"
    
    # Create and start bot
    bot = MomentumTsunamiBot(config)
    await bot.start()


if __name__ == "__main__":
    asyncio.run(main())
