#!/usr/bin/env python3
"""
Logging utilities for MomentumTsunami trading bot.
"""

import os
import sys
from pathlib import Path
from loguru import logger
from typing import Optional


def setup_logger(name: str, config) -> logger:
    """
    Setup logger with configuration.
    
    Args:
        name: Logger name
        config: Logging configuration
        
    Returns:
        Configured logger instance
    """
    # Remove default handler
    logger.remove()
    
    # Create log directory
    log_dir = Path(config.log_dir)
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Console handler
    logger.add(
        sys.stdout,
        level=config.level,
        format=config.format,
        colorize=True,
        backtrace=True,
        diagnose=True
    )
    
    # File handler for general logs
    logger.add(
        log_dir / f"{name.lower()}.log",
        level=config.level,
        format=config.format,
        rotation=config.max_file_size,
        retention=config.backup_count,
        compression="zip",
        backtrace=True,
        diagnose=True
    )
    
    # Separate file for errors
    logger.add(
        log_dir / f"{name.lower()}_errors.log",
        level="ERROR",
        format=config.format,
        rotation=config.max_file_size,
        retention=config.backup_count,
        compression="zip",
        backtrace=True,
        diagnose=True
    )
    
    # Trading-specific log file
    logger.add(
        log_dir / "trades.log",
        level="INFO",
        format="{time:YYYY-MM-DD HH:mm:ss} | {message}",
        filter=lambda record: "TRADE" in record["message"],
        rotation="1 day",
        retention="30 days",
        compression="zip"
    )
    
    return logger


def log_trade(action: str, symbol: str, price: float, quantity: int, 
              pnl: Optional[float] = None, reason: Optional[str] = None):
    """
    Log trading activity.
    
    Args:
        action: Trade action (BUY/SELL)
        symbol: Stock symbol
        price: Trade price
        quantity: Number of shares
        pnl: Profit/loss (for sells)
        reason: Trade reason/strategy
    """
    message = f"TRADE | {action} | {symbol} | ${price:.2f} | {quantity} shares"
    
    if pnl is not None:
        message += f" | P&L: ${pnl:.2f}"
    
    if reason:
        message += f" | {reason}"
    
    logger.info(message)


def log_performance(daily_pnl: float, win_rate: float, total_trades: int):
    """
    Log daily performance metrics.
    
    Args:
        daily_pnl: Daily profit/loss
        win_rate: Win rate percentage
        total_trades: Total number of trades
    """
    logger.info(f"PERFORMANCE | Daily P&L: ${daily_pnl:.2f} | Win Rate: {win_rate:.1f}% | Trades: {total_trades}")


def log_risk_event(event_type: str, symbol: str, details: str):
    """
    Log risk management events.
    
    Args:
        event_type: Type of risk event
        symbol: Stock symbol
        details: Event details
    """
    logger.warning(f"RISK | {event_type} | {symbol} | {details}")


def log_market_event(event_type: str, details: str):
    """
    Log market-related events.
    
    Args:
        event_type: Type of market event
        details: Event details
    """
    logger.info(f"MARKET | {event_type} | {details}")


class TradingLogger:
    """
    Specialized logger for trading activities.
    """
    
    def __init__(self, config):
        self.logger = setup_logger("Trading", config)
        self.config = config
    
    def trade(self, action: str, symbol: str, price: float, quantity: int, 
              pnl: Optional[float] = None, reason: Optional[str] = None):
        """Log trade execution."""
        log_trade(action, symbol, price, quantity, pnl, reason)
    
    def performance(self, daily_pnl: float, win_rate: float, total_trades: int):
        """Log performance metrics."""
        log_performance(daily_pnl, win_rate, total_trades)
    
    def risk(self, event_type: str, symbol: str, details: str):
        """Log risk events."""
        log_risk_event(event_type, symbol, details)
    
    def market(self, event_type: str, details: str):
        """Log market events."""
        log_market_event(event_type, details)
    
    def info(self, message: str):
        """Log info message."""
        self.logger.info(message)
    
    def warning(self, message: str):
        """Log warning message."""
        self.logger.warning(message)
    
    def error(self, message: str):
        """Log error message."""
        self.logger.error(message)
    
    def debug(self, message: str):
        """Log debug message."""
        self.logger.debug(message)
