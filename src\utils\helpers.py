#!/usr/bin/env python3
"""
Helper utilities for MomentumTsunami trading bot.
"""

import asyncio
import time
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Optional, Union
import pandas as pd
import numpy as np
from functools import wraps


def is_market_hours() -> bool:
    """
    Check if current time is during market hours (9:30 AM - 4:00 PM EST).
    
    Returns:
        True if market is open, False otherwise
    """
    now = datetime.now(timezone.utc)
    # Convert to EST
    est_time = now - timedelta(hours=5)  # Simplified, doesn't account for DST
    
    # Check if weekday
    if est_time.weekday() >= 5:  # Saturday = 5, Sunday = 6
        return False
    
    # Check market hours (9:30 AM - 4:00 PM EST)
    market_open = est_time.replace(hour=9, minute=30, second=0, microsecond=0)
    market_close = est_time.replace(hour=16, minute=0, second=0, microsecond=0)
    
    return market_open <= est_time <= market_close


def is_pre_market() -> bool:
    """
    Check if current time is during pre-market hours (4:00 AM - 9:30 AM EST).
    
    Returns:
        True if pre-market, False otherwise
    """
    now = datetime.now(timezone.utc)
    est_time = now - timedelta(hours=5)
    
    if est_time.weekday() >= 5:
        return False
    
    pre_market_start = est_time.replace(hour=4, minute=0, second=0, microsecond=0)
    market_open = est_time.replace(hour=9, minute=30, second=0, microsecond=0)
    
    return pre_market_start <= est_time <= market_open


def calculate_gap_percentage(current_price: float, previous_close: float) -> float:
    """
    Calculate gap percentage.
    
    Args:
        current_price: Current stock price
        previous_close: Previous day's closing price
        
    Returns:
        Gap percentage
    """
    if previous_close == 0:
        return 0.0
    
    return ((current_price - previous_close) / previous_close) * 100


def calculate_position_size(account_size: float, risk_percent: float, 
                          entry_price: float, stop_loss: float) -> int:
    """
    Calculate position size based on risk management rules.
    
    Args:
        account_size: Total account size
        risk_percent: Risk percentage per trade
        entry_price: Entry price per share
        stop_loss: Stop loss price per share
        
    Returns:
        Number of shares to buy
    """
    if entry_price <= stop_loss:
        return 0
    
    risk_amount = account_size * (risk_percent / 100)
    risk_per_share = entry_price - stop_loss
    
    if risk_per_share <= 0:
        return 0
    
    position_size = int(risk_amount / risk_per_share)
    
    # Ensure we don't exceed account buying power
    max_shares = int(account_size * 0.25 / entry_price)  # Use max 25% of account
    
    return min(position_size, max_shares)


def calculate_profit_target(entry_price: float, stop_loss: float, 
                          risk_reward_ratio: float = 1.5) -> float:
    """
    Calculate profit target based on risk-reward ratio.
    
    Args:
        entry_price: Entry price
        stop_loss: Stop loss price
        risk_reward_ratio: Risk-reward ratio
        
    Returns:
        Profit target price
    """
    risk_per_share = entry_price - stop_loss
    reward_per_share = risk_per_share * risk_reward_ratio
    
    return entry_price + reward_per_share


def format_currency(amount: float) -> str:
    """
    Format currency amount.
    
    Args:
        amount: Currency amount
        
    Returns:
        Formatted currency string
    """
    return f"${amount:,.2f}"


def format_percentage(value: float) -> str:
    """
    Format percentage value.
    
    Args:
        value: Percentage value
        
    Returns:
        Formatted percentage string
    """
    return f"{value:.2f}%"


def retry_async(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """
    Async retry decorator.
    
    Args:
        max_retries: Maximum number of retries
        delay: Initial delay between retries
        backoff: Backoff multiplier
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries:
                        raise e
                    
                    await asyncio.sleep(current_delay)
                    current_delay *= backoff
            
        return wrapper
    return decorator


def validate_symbol(symbol: str) -> bool:
    """
    Validate stock symbol format.
    
    Args:
        symbol: Stock symbol
        
    Returns:
        True if valid, False otherwise
    """
    if not symbol or not isinstance(symbol, str):
        return False
    
    # Basic validation: 1-5 uppercase letters
    return symbol.isalpha() and symbol.isupper() and 1 <= len(symbol) <= 5


def calculate_atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
    """
    Calculate Average True Range (ATR).
    
    Args:
        high: High prices
        low: Low prices
        close: Close prices
        period: ATR period
        
    Returns:
        ATR values
    """
    tr1 = high - low
    tr2 = abs(high - close.shift(1))
    tr3 = abs(low - close.shift(1))
    
    true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    return true_range.rolling(window=period).mean()


def calculate_ema(prices: pd.Series, period: int) -> pd.Series:
    """
    Calculate Exponential Moving Average (EMA).
    
    Args:
        prices: Price series
        period: EMA period
        
    Returns:
        EMA values
    """
    return prices.ewm(span=period, adjust=False).mean()


def calculate_rsi(prices: pd.Series, period: int = 14) -> pd.Series:
    """
    Calculate Relative Strength Index (RSI).
    
    Args:
        prices: Price series
        period: RSI period
        
    Returns:
        RSI values
    """
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    
    return rsi


def calculate_volume_ratio(current_volume: float, avg_volume: float) -> float:
    """
    Calculate volume ratio compared to average.
    
    Args:
        current_volume: Current volume
        avg_volume: Average volume
        
    Returns:
        Volume ratio
    """
    if avg_volume == 0:
        return 0.0
    
    return current_volume / avg_volume


def is_trading_day(date: datetime) -> bool:
    """
    Check if given date is a trading day (weekday, not holiday).
    
    Args:
        date: Date to check
        
    Returns:
        True if trading day, False otherwise
    """
    # Basic check for weekday (doesn't include market holidays)
    return date.weekday() < 5


def get_market_calendar(start_date: datetime, end_date: datetime) -> List[datetime]:
    """
    Get list of trading days between start and end dates.
    
    Args:
        start_date: Start date
        end_date: End date
        
    Returns:
        List of trading days
    """
    trading_days = []
    current_date = start_date
    
    while current_date <= end_date:
        if is_trading_day(current_date):
            trading_days.append(current_date)
        current_date += timedelta(days=1)
    
    return trading_days


def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """
    Safely divide two numbers, returning default if denominator is zero.
    
    Args:
        numerator: Numerator
        denominator: Denominator
        default: Default value if division by zero
        
    Returns:
        Division result or default
    """
    if denominator == 0:
        return default
    
    return numerator / denominator


def round_to_tick(price: float, tick_size: float = 0.01) -> float:
    """
    Round price to nearest tick size.
    
    Args:
        price: Price to round
        tick_size: Tick size
        
    Returns:
        Rounded price
    """
    return round(price / tick_size) * tick_size


def get_timestamp() -> str:
    """
    Get current timestamp string.
    
    Returns:
        Formatted timestamp
    """
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def convert_timeframe(timeframe: str) -> int:
    """
    Convert timeframe string to minutes.
    
    Args:
        timeframe: Timeframe string (e.g., '1m', '5m', '1h')
        
    Returns:
        Minutes as integer
    """
    timeframe_map = {
        '1m': 1,
        '5m': 5,
        '15m': 15,
        '30m': 30,
        '1h': 60,
        '4h': 240,
        '1d': 1440
    }
    
    return timeframe_map.get(timeframe.lower(), 1)
