#!/usr/bin/env python3
"""
Main trading strategy for MomentumTsunami trading bot.
Implements gap-up momentum strategy with pullback entries.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import pandas as pd

from .indicators import TechnicalIndicators
from .signals import SignalGenerator
from utils.logger import setup_logger


class MomentumStrategy:
    """
    Gap-up momentum trading strategy with pullback entries.
    
    Strategy Rules:
    1. Identify stocks gapping up 3-8% at market open
    2. Wait for pullback to 20 EMA after initial surge
    3. Enter long when price bounces off EMA with volume confirmation
    4. Set stop loss below gap fill level or 2% below entry
    5. Take profit at 1.5R or trail stop using ATR
    """
    
    def __init__(self, config):
        self.config = config
        self.logger = setup_logger("MomentumStrategy", config.logging)
        
        # Initialize components
        self.indicators = TechnicalIndicators(config)
        self.signal_generator = SignalGenerator(config)
        
        # Strategy parameters
        self.ema_period = config.trading.ema_period
        self.rsi_period = config.trading.rsi_period
        self.volume_multiplier = config.trading.volume_surge_multiplier
        self.profit_target_ratio = config.trading.profit_target_ratio
        self.stop_loss_percent = config.trading.stop_loss_percent
        self.trailing_stop_multiplier = config.trading.trailing_stop_atr_multiplier
        
        # Strategy state
        self.active_signals = {}
        self.signal_history = []
        
        self.logger.info("MomentumStrategy initialized")
    
    async def analyze_stock(self, stock_data: Dict[str, Any], data_feed) -> Dict[str, Any]:
        """
        Analyze a gap-up stock for trading opportunities.
        
        Args:
            stock_data: Gap stock data from scanner
            data_feed: Data feed instance for getting market data
            
        Returns:
            Analysis results dictionary
        """
        symbol = stock_data['symbol']
        
        try:
            # Get intraday data (5-minute bars)
            intraday_data = await data_feed.get_intraday_data(symbol, "5min")
            
            if intraday_data.empty:
                return {
                    'symbol': symbol,
                    'analysis_status': 'insufficient_data',
                    'recommendation': 'SKIP',
                    'reason': 'No intraday data available'
                }
            
            # Calculate technical indicators
            indicators = self.indicators.calculate_momentum_indicators(intraday_data)
            
            # Analyze current market conditions
            analysis = await self._analyze_market_conditions(stock_data, intraday_data, indicators)
            
            # Generate recommendation
            recommendation = await self._generate_recommendation(stock_data, intraday_data, indicators, analysis)
            
            return {
                'symbol': symbol,
                'analysis_status': 'complete',
                'current_price': intraday_data['close'].iloc[-1],
                'gap_percent': stock_data['gap_percent'],
                'analysis': analysis,
                'recommendation': recommendation['action'],
                'reason': recommendation['reason'],
                'entry_score': recommendation.get('entry_score', 0),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing {symbol}: {e}")
            return {
                'symbol': symbol,
                'analysis_status': 'error',
                'recommendation': 'SKIP',
                'reason': f'Analysis error: {str(e)}'
            }
    
    async def _analyze_market_conditions(self, stock_data: Dict[str, Any], 
                                       market_data: pd.DataFrame, 
                                       indicators: Dict[str, pd.Series]) -> Dict[str, Any]:
        """Analyze current market conditions for the stock."""
        
        current_price = market_data['close'].iloc[-1]
        current_volume = market_data['volume'].iloc[-1]
        
        analysis = {
            'price_action': {},
            'volume_analysis': {},
            'technical_indicators': {},
            'gap_analysis': {},
            'momentum_analysis': {}
        }
        
        # Price action analysis
        ema_20 = indicators['ema_20'][-1]
        if not pd.isna(ema_20):
            ema_distance = ((current_price - ema_20) / current_price) * 100
            analysis['price_action'] = {
                'current_price': current_price,
                'ema_20': ema_20,
                'distance_from_ema_percent': ema_distance,
                'near_ema': abs(ema_distance) <= 1.0,
                'above_ema': current_price > ema_20
            }
        
        # Volume analysis
        avg_volume = indicators['volume_sma'][-1]
        if not pd.isna(avg_volume) and avg_volume > 0:
            volume_ratio = current_volume / avg_volume
            analysis['volume_analysis'] = {
                'current_volume': current_volume,
                'average_volume': avg_volume,
                'volume_ratio': volume_ratio,
                'volume_surge': volume_ratio >= self.volume_multiplier,
                'volume_strength': 'high' if volume_ratio >= 2.0 else 'medium' if volume_ratio >= 1.0 else 'low'
            }
        
        # Technical indicators analysis
        rsi = indicators['rsi'][-1]
        macd = indicators['macd'][-1]
        macd_signal = indicators['macd_signal'][-1]
        atr = indicators['atr'][-1]
        
        analysis['technical_indicators'] = {
            'rsi': rsi,
            'rsi_favorable': self.config.trading.rsi_min <= rsi <= self.config.trading.rsi_max if not pd.isna(rsi) else None,
            'macd_bullish': macd > macd_signal if not (pd.isna(macd) or pd.isna(macd_signal)) else None,
            'atr': atr,
            'volatility_percent': (atr / current_price) * 100 if not pd.isna(atr) else None
        }
        
        # Gap analysis
        gap_fill_level = stock_data['gap_fill_level']
        distance_from_gap_fill = ((current_price - gap_fill_level) / gap_fill_level) * 100
        
        analysis['gap_analysis'] = {
            'original_gap_percent': stock_data['gap_percent'],
            'gap_fill_level': gap_fill_level,
            'distance_from_gap_fill_percent': distance_from_gap_fill,
            'gap_integrity': current_price > gap_fill_level * 1.01,
            'gap_fade_risk': distance_from_gap_fill < 2.0  # Less than 2% above gap fill
        }
        
        # Momentum analysis
        analysis['momentum_analysis'] = {
            'bullish_momentum': self.indicators.is_bullish_momentum(market_data, indicators),
            'bearish_momentum': self.indicators.is_bearish_momentum(market_data, indicators),
            'momentum_strength': self._assess_momentum_strength(market_data, indicators)
        }
        
        return analysis
    
    def _assess_momentum_strength(self, market_data: pd.DataFrame, indicators: Dict[str, pd.Series]) -> str:
        """Assess overall momentum strength."""
        
        # Calculate momentum score
        score = 0
        
        # Price vs EMA
        current_price = market_data['close'].iloc[-1]
        ema_20 = indicators['ema_20'][-1]
        if not pd.isna(ema_20) and current_price > ema_20:
            score += 1
        
        # RSI in favorable range
        rsi = indicators['rsi'][-1]
        if not pd.isna(rsi) and 40 <= rsi <= 70:
            score += 1
        
        # MACD bullish
        macd = indicators['macd'][-1]
        macd_signal = indicators['macd_signal'][-1]
        if not (pd.isna(macd) or pd.isna(macd_signal)) and macd > macd_signal:
            score += 1
        
        # Volume above average
        current_volume = market_data['volume'].iloc[-1]
        avg_volume = indicators['volume_sma'][-1]
        if not pd.isna(avg_volume) and current_volume > avg_volume:
            score += 1
        
        # Recent price action (last 3 bars trending up)
        if len(market_data) >= 3:
            recent_closes = market_data['close'].tail(3)
            if recent_closes.iloc[-1] > recent_closes.iloc[-2] > recent_closes.iloc[-3]:
                score += 1
        
        # Classify momentum strength
        if score >= 4:
            return 'strong'
        elif score >= 2:
            return 'moderate'
        else:
            return 'weak'
    
    async def _generate_recommendation(self, stock_data: Dict[str, Any], 
                                     market_data: pd.DataFrame, 
                                     indicators: Dict[str, pd.Series],
                                     analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate trading recommendation based on analysis."""
        
        symbol = stock_data['symbol']
        
        # Check if conditions are met for entry
        entry_signal = await self.signal_generator.generate_entry_signal(stock_data, market_data)
        
        if entry_signal:
            return {
                'action': 'BUY',
                'reason': 'All entry conditions met - pullback to EMA with momentum',
                'entry_score': entry_signal['entry_score'],
                'entry_price': entry_signal['price'],
                'stop_loss': entry_signal['stop_loss'],
                'profit_target': entry_signal['profit_target'],
                'risk_reward_ratio': entry_signal['risk_reward_ratio']
            }
        
        # Analyze why entry conditions are not met
        price_action = analysis['price_action']
        volume_analysis = analysis['volume_analysis']
        technical = analysis['technical_indicators']
        gap_analysis = analysis['gap_analysis']
        momentum = analysis['momentum_analysis']
        
        # Determine specific reason for no entry
        if not gap_analysis.get('gap_integrity', False):
            return {
                'action': 'SKIP',
                'reason': 'Gap integrity compromised - too close to gap fill level'
            }
        
        if not price_action.get('near_ema', False):
            if price_action.get('distance_from_ema_percent', 0) > 3:
                return {
                    'action': 'WATCH',
                    'reason': 'Price too far above EMA - waiting for pullback'
                }
            elif price_action.get('distance_from_ema_percent', 0) < -2:
                return {
                    'action': 'SKIP',
                    'reason': 'Price below EMA - momentum broken'
                }
        
        if not volume_analysis.get('volume_surge', False):
            return {
                'action': 'WATCH',
                'reason': 'Insufficient volume - waiting for volume confirmation'
            }
        
        if not technical.get('rsi_favorable', True):
            rsi = technical.get('rsi', 50)
            if rsi > self.config.trading.rsi_max:
                return {
                    'action': 'SKIP',
                    'reason': f'RSI overbought ({rsi:.1f}) - avoid entry'
                }
            elif rsi < self.config.trading.rsi_min:
                return {
                    'action': 'SKIP',
                    'reason': f'RSI oversold ({rsi:.1f}) - momentum weak'
                }
        
        if momentum['momentum_strength'] == 'weak':
            return {
                'action': 'SKIP',
                'reason': 'Weak momentum - multiple indicators unfavorable'
            }
        
        # Default to watch if conditions are close but not quite met
        return {
            'action': 'WATCH',
            'reason': 'Conditions partially met - monitoring for entry opportunity'
        }
    
    async def check_entry_conditions(self, stock_data: Dict[str, Any], market_data: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """
        Check if entry conditions are met for a gap-up stock.
        
        Args:
            stock_data: Gap stock data
            market_data: Current market data
            
        Returns:
            Entry signal dictionary or None
        """
        return await self.signal_generator.generate_entry_signal(stock_data, market_data)
    
    async def check_exit_conditions(self, position: Dict[str, Any], market_data: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """
        Check if exit conditions are met for an existing position.
        
        Args:
            position: Current position data
            market_data: Current market data
            
        Returns:
            Exit signal dictionary or None
        """
        return await self.signal_generator.generate_exit_signal(position, market_data)
    
    async def update_position_management(self, position: Dict[str, Any], market_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Update position management (trailing stops, etc.).
        
        Args:
            position: Current position data
            market_data: Current market data
            
        Returns:
            Updated position data
        """
        updated_position = position.copy()
        
        # Update trailing stop if applicable
        new_stop = await self.signal_generator.update_trailing_stop(position, market_data)
        if new_stop:
            updated_position['stop_loss'] = new_stop
            updated_position['trailing_stop_updated'] = datetime.now().isoformat()
        
        return updated_position
    
    def get_strategy_stats(self) -> Dict[str, Any]:
        """Get strategy performance statistics."""
        return {
            'strategy_name': 'Gap Momentum Pullback',
            'active_signals': len(self.active_signals),
            'total_signals_generated': len(self.signal_history),
            'parameters': {
                'ema_period': self.ema_period,
                'rsi_period': self.rsi_period,
                'volume_multiplier': self.volume_multiplier,
                'profit_target_ratio': self.profit_target_ratio,
                'stop_loss_percent': self.stop_loss_percent,
                'trailing_stop_multiplier': self.trailing_stop_multiplier
            }
        }
    
    def add_signal_to_history(self, signal: Dict[str, Any]):
        """Add signal to history for tracking."""
        signal['id'] = len(self.signal_history)
        self.signal_history.append(signal)
        
        if signal['signal_type'] == 'ENTRY_LONG':
            self.active_signals[signal['symbol']] = signal
        elif signal['signal_type'] == 'EXIT_LONG':
            self.active_signals.pop(signal['symbol'], None)
