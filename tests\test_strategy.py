#!/usr/bin/env python3
"""
Tests for MomentumTsunami trading strategy.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from config import Config
from strategy.strategy import MomentumStrategy
from strategy.indicators import TechnicalIndicators
from strategy.signals import SignalGenerator


class TestTechnicalIndicators:
    """Test technical indicators calculations."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        config = Config()
        config.logging.level = "ERROR"  # Suppress logs during testing
        return config
    
    @pytest.fixture
    def indicators(self, config):
        """Create indicators instance."""
        return TechnicalIndicators(config)
    
    @pytest.fixture
    def sample_data(self):
        """Create sample OHLCV data."""
        dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
        np.random.seed(42)  # For reproducible tests
        
        # Generate realistic price data
        base_price = 100
        prices = []
        volumes = []
        
        for i in range(100):
            daily_return = np.random.normal(0.001, 0.02)
            base_price *= (1 + daily_return)
            
            high = base_price * (1 + abs(np.random.normal(0, 0.01)))
            low = base_price * (1 - abs(np.random.normal(0, 0.01)))
            open_price = base_price * (1 + np.random.normal(0, 0.005))
            close = base_price
            volume = int(np.random.uniform(1000000, 5000000))
            
            prices.append([open_price, high, low, close])
            volumes.append(volume)
        
        df = pd.DataFrame(prices, columns=['open', 'high', 'low', 'close'], index=dates)
        df['volume'] = volumes
        
        return df
    
    def test_ema_calculation(self, indicators, sample_data):
        """Test EMA calculation."""
        ema_20 = indicators.calculate_ema(sample_data['close'], 20)
        
        assert len(ema_20) == len(sample_data)
        assert not np.isnan(ema_20[-1])  # Last value should not be NaN
        assert ema_20[-1] > 0  # Should be positive
    
    def test_rsi_calculation(self, indicators, sample_data):
        """Test RSI calculation."""
        rsi = indicators.calculate_rsi(sample_data['close'], 14)
        
        assert len(rsi) == len(sample_data)
        # RSI should be between 0 and 100
        valid_rsi = rsi[~np.isnan(rsi)]
        assert all(0 <= val <= 100 for val in valid_rsi)
    
    def test_atr_calculation(self, indicators, sample_data):
        """Test ATR calculation."""
        atr = indicators.calculate_atr(
            sample_data['high'], 
            sample_data['low'], 
            sample_data['close'], 
            14
        )
        
        assert len(atr) == len(sample_data)
        # ATR should be positive
        valid_atr = atr[~np.isnan(atr)]
        assert all(val >= 0 for val in valid_atr)
    
    def test_momentum_indicators(self, indicators, sample_data):
        """Test momentum indicators calculation."""
        momentum_indicators = indicators.calculate_momentum_indicators(sample_data)
        
        expected_indicators = [
            'ema_20', 'ema_50', 'sma_20', 'rsi', 'atr',
            'bb_upper', 'bb_middle', 'bb_lower',
            'macd', 'macd_signal', 'macd_histogram',
            'volume_sma', 'vwap'
        ]
        
        for indicator in expected_indicators:
            assert indicator in momentum_indicators
            assert len(momentum_indicators[indicator]) == len(sample_data)
    
    def test_bullish_momentum_detection(self, indicators, sample_data):
        """Test bullish momentum detection."""
        momentum_indicators = indicators.calculate_momentum_indicators(sample_data)
        is_bullish = indicators.is_bullish_momentum(sample_data, momentum_indicators)
        
        assert isinstance(is_bullish, bool)
    
    def test_entry_score_calculation(self, indicators, sample_data):
        """Test entry score calculation."""
        momentum_indicators = indicators.calculate_momentum_indicators(sample_data)
        entry_score = indicators.calculate_entry_score(sample_data, momentum_indicators)
        
        assert 0 <= entry_score <= 1
    
    def test_trailing_stop_calculation(self, indicators):
        """Test trailing stop calculation."""
        entry_price = 100.0
        current_price = 105.0
        atr = 2.0
        
        trailing_stop = indicators.get_trailing_stop_level(
            entry_price, current_price, atr, 0.5
        )
        
        assert trailing_stop > 0
        assert trailing_stop < current_price
        assert trailing_stop >= entry_price * 0.98  # Should not be below 2% stop


class TestSignalGenerator:
    """Test signal generation logic."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        config = Config()
        config.logging.level = "ERROR"
        return config
    
    @pytest.fixture
    def signal_generator(self, config):
        """Create signal generator instance."""
        return SignalGenerator(config)
    
    @pytest.fixture
    def gap_stock_data(self):
        """Create sample gap stock data."""
        return {
            'symbol': 'TEST',
            'current_price': 105.0,
            'previous_close': 100.0,
            'gap_percent': 5.0,
            'gap_fill_level': 100.0,
            'volume': 2000000,
            'avg_volume': 1000000,
            'volume_ratio': 2.0
        }
    
    @pytest.fixture
    def market_data(self):
        """Create sample market data."""
        dates = pd.date_range(start='2023-01-01', periods=50, freq='5min')
        np.random.seed(42)
        
        base_price = 105.0
        data = []
        
        for i in range(50):
            daily_return = np.random.normal(0, 0.001)
            base_price *= (1 + daily_return)
            
            high = base_price * (1 + abs(np.random.normal(0, 0.002)))
            low = base_price * (1 - abs(np.random.normal(0, 0.002)))
            open_price = base_price * (1 + np.random.normal(0, 0.001))
            close = base_price
            volume = int(np.random.uniform(10000, 50000))
            
            data.append([open_price, high, low, close, volume])
        
        df = pd.DataFrame(data, columns=['open', 'high', 'low', 'close', 'volume'], index=dates)
        return df
    
    @pytest.mark.asyncio
    async def test_entry_signal_generation(self, signal_generator, gap_stock_data, market_data):
        """Test entry signal generation."""
        entry_signal = await signal_generator.generate_entry_signal(gap_stock_data, market_data)
        
        # Signal might be None if conditions not met, which is valid
        if entry_signal:
            assert 'symbol' in entry_signal
            assert 'signal_type' in entry_signal
            assert 'price' in entry_signal
            assert 'stop_loss' in entry_signal
            assert 'profit_target' in entry_signal
            assert entry_signal['signal_type'] == 'ENTRY_LONG'
    
    @pytest.mark.asyncio
    async def test_exit_signal_generation(self, signal_generator, market_data):
        """Test exit signal generation."""
        position = {
            'symbol': 'TEST',
            'entry_price': 100.0,
            'quantity': 100,
            'stop_loss': 98.0,
            'profit_target': 103.0,
            'entry_time': datetime.now() - timedelta(hours=1),
            'gap_fill_level': 99.0
        }
        
        exit_signal = await signal_generator.generate_exit_signal(position, market_data)
        
        # Signal might be None if no exit conditions met
        if exit_signal:
            assert 'symbol' in exit_signal
            assert 'signal_type' in exit_signal
            assert 'reason' in exit_signal
            assert exit_signal['signal_type'] == 'EXIT_LONG'
    
    def test_signal_strength_calculation(self, signal_generator):
        """Test signal strength calculation."""
        conditions = {
            'near_ema': True,
            'rsi_favorable': True,
            'volume_surge': False,
            'bullish_momentum': True,
            'gap_integrity': True,
            'time_valid': True,
            'macd_bullish': False
        }
        
        strength = signal_generator.calculate_signal_strength(conditions)
        
        assert 0 <= strength <= 1
        assert strength > 0  # Should be positive with some conditions met


class TestMomentumStrategy:
    """Test main momentum strategy."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        config = Config()
        config.logging.level = "ERROR"
        return config
    
    @pytest.fixture
    def strategy(self, config):
        """Create strategy instance."""
        return MomentumStrategy(config)
    
    @pytest.fixture
    def gap_stock_data(self):
        """Create sample gap stock data."""
        return {
            'symbol': 'TEST',
            'current_price': 105.0,
            'previous_close': 100.0,
            'gap_percent': 5.0,
            'gap_fill_level': 100.0,
            'volume': 2000000,
            'avg_volume': 1000000,
            'volume_ratio': 2.0,
            'market_cap': 10000000000,
            'float_shares': 100000000
        }
    
    @pytest.fixture
    def market_data(self):
        """Create sample market data."""
        dates = pd.date_range(start='2023-01-01', periods=50, freq='5min')
        np.random.seed(42)
        
        base_price = 105.0
        data = []
        
        for i in range(50):
            daily_return = np.random.normal(0, 0.001)
            base_price *= (1 + daily_return)
            
            high = base_price * (1 + abs(np.random.normal(0, 0.002)))
            low = base_price * (1 - abs(np.random.normal(0, 0.002)))
            open_price = base_price * (1 + np.random.normal(0, 0.001))
            close = base_price
            volume = int(np.random.uniform(10000, 50000))
            
            data.append([open_price, high, low, close, volume])
        
        df = pd.DataFrame(data, columns=['open', 'high', 'low', 'close', 'volume'], index=dates)
        return df
    
    @pytest.mark.asyncio
    async def test_stock_analysis(self, strategy, gap_stock_data):
        """Test stock analysis functionality."""
        # Mock data feed
        class MockDataFeed:
            async def get_intraday_data(self, symbol, interval):
                # Return sample data
                dates = pd.date_range(start='2023-01-01', periods=30, freq='5min')
                data = []
                base_price = 105.0
                
                for i in range(30):
                    data.append([base_price, base_price * 1.01, base_price * 0.99, base_price, 10000])
                
                return pd.DataFrame(data, columns=['open', 'high', 'low', 'close', 'volume'], index=dates)
        
        mock_data_feed = MockDataFeed()
        analysis = await strategy.analyze_stock(gap_stock_data, mock_data_feed)
        
        assert 'symbol' in analysis
        assert 'analysis_status' in analysis
        assert 'recommendation' in analysis
        assert analysis['symbol'] == 'TEST'
    
    @pytest.mark.asyncio
    async def test_entry_conditions_check(self, strategy, gap_stock_data, market_data):
        """Test entry conditions checking."""
        entry_signal = await strategy.check_entry_conditions(gap_stock_data, market_data)
        
        # Signal might be None if conditions not met
        if entry_signal:
            assert isinstance(entry_signal, dict)
            assert 'symbol' in entry_signal
    
    @pytest.mark.asyncio
    async def test_exit_conditions_check(self, strategy, market_data):
        """Test exit conditions checking."""
        position = {
            'symbol': 'TEST',
            'entry_price': 100.0,
            'quantity': 100,
            'stop_loss': 98.0,
            'profit_target': 103.0,
            'entry_time': datetime.now() - timedelta(hours=1),
            'gap_fill_level': 99.0
        }
        
        exit_signal = await strategy.check_exit_conditions(position, market_data)
        
        # Signal might be None if no exit conditions met
        if exit_signal:
            assert isinstance(exit_signal, dict)
            assert 'symbol' in exit_signal
    
    def test_strategy_stats(self, strategy):
        """Test strategy statistics."""
        stats = strategy.get_strategy_stats()
        
        assert 'strategy_name' in stats
        assert 'parameters' in stats
        assert stats['strategy_name'] == 'Gap Momentum Pullback'


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
