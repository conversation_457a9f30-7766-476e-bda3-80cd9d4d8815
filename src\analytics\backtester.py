#!/usr/bin/env python3
"""
Backtesting framework for MomentumTsunami trading bot.
"""

import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from pathlib import Path

from strategy.strategy import MomentumStrategy
from strategy.indicators import TechnicalIndicators
from risk.risk_manager import RiskManager
from analytics.performance_tracker import PerformanceTracker, TradeRecord
from utils.logger import setup_logger
from utils.helpers import calculate_gap_percentage


class BacktestEngine:
    """
    Backtesting engine for strategy validation and optimization.
    """
    
    def __init__(self, config):
        self.config = config
        self.logger = setup_logger("BacktestEngine", config.logging)
        
        # Initialize components
        self.strategy = MomentumStrategy(config)
        self.indicators = TechnicalIndicators(config)
        self.risk_manager = RiskManager(config)
        self.performance_tracker = PerformanceTracker(config)
        
        # Backtest state
        self.current_date = None
        self.positions = {}
        self.cash = config.account.account_size
        self.initial_capital = config.account.account_size
        
        # Backtest results
        self.trades = []
        self.daily_equity = []
        self.backtest_stats = {}
        
        self.logger.info("Backtest Engine initialized")
    
    async def run_backtest(self, start_date: str, end_date: str, 
                          data_source: str = "yfinance") -> Dict[str, Any]:
        """
        Run backtest over specified date range.
        
        Args:
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            data_source: Data source for historical data
            
        Returns:
            Backtest results dictionary
        """
        self.logger.info(f"Starting backtest from {start_date} to {end_date}")
        
        try:
            # Reset backtest state
            self._reset_backtest_state()
            
            # Get historical data
            historical_data = await self._load_historical_data(start_date, end_date, data_source)
            
            if not historical_data:
                raise ValueError("No historical data available for backtest")
            
            # Run simulation
            await self._run_simulation(historical_data)
            
            # Calculate final statistics
            self.backtest_stats = self._calculate_backtest_stats()
            
            self.logger.info(f"Backtest completed - Total P&L: ${self.backtest_stats['total_pnl']:,.2f}")
            
            return {
                'start_date': start_date,
                'end_date': end_date,
                'initial_capital': self.initial_capital,
                'final_equity': self.cash + self._calculate_position_value(),
                'total_trades': len(self.trades),
                'statistics': self.backtest_stats,
                'trades': [trade.to_dict() for trade in self.trades],
                'equity_curve': self.daily_equity
            }
            
        except Exception as e:
            self.logger.error(f"Backtest failed: {e}")
            return {'error': str(e)}
    
    def _reset_backtest_state(self):
        """Reset backtest state for new run."""
        self.positions = {}
        self.cash = self.initial_capital
        self.trades = []
        self.daily_equity = []
        self.risk_manager.reset_daily_tracking()
    
    async def _load_historical_data(self, start_date: str, end_date: str, 
                                  data_source: str) -> Dict[str, pd.DataFrame]:
        """Load historical data for backtesting."""
        # For demo purposes, we'll simulate loading data
        # In production, this would load real historical data
        
        self.logger.info(f"Loading historical data from {data_source}")
        
        # Simulate some gap-up stocks data
        symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'AMZN', 'META', 'NVDA']
        historical_data = {}
        
        # Generate synthetic data for demonstration
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        
        for symbol in symbols:
            # Generate synthetic OHLCV data
            np.random.seed(hash(symbol) % 2**32)  # Consistent random data per symbol
            
            base_price = np.random.uniform(50, 300)
            prices = []
            volumes = []
            
            for i, date in enumerate(date_range):
                # Skip weekends
                if date.weekday() >= 5:
                    continue
                
                # Simulate price movement
                daily_return = np.random.normal(0.001, 0.02)  # 0.1% mean, 2% std
                
                # Occasionally simulate gap-ups (5% chance)
                if np.random.random() < 0.05:
                    gap_return = np.random.uniform(0.03, 0.08)  # 3-8% gap
                    daily_return += gap_return
                
                base_price *= (1 + daily_return)
                
                # Generate OHLC
                high = base_price * np.random.uniform(1.0, 1.02)
                low = base_price * np.random.uniform(0.98, 1.0)
                open_price = base_price * np.random.uniform(0.99, 1.01)
                close = base_price
                volume = int(np.random.uniform(1000000, 5000000))
                
                prices.append({
                    'date': date,
                    'open': open_price,
                    'high': high,
                    'low': low,
                    'close': close,
                    'volume': volume
                })
            
            if prices:
                df = pd.DataFrame(prices)
                df.set_index('date', inplace=True)
                historical_data[symbol] = df
        
        self.logger.info(f"Loaded data for {len(historical_data)} symbols")
        return historical_data
    
    async def _run_simulation(self, historical_data: Dict[str, pd.DataFrame]):
        """Run the backtesting simulation."""
        # Get all unique dates
        all_dates = set()
        for df in historical_data.values():
            all_dates.update(df.index)
        
        sorted_dates = sorted(all_dates)
        
        for date in sorted_dates:
            self.current_date = date
            
            # Check for gap-up opportunities
            gap_stocks = await self._scan_for_gaps(historical_data, date)
            
            # Process existing positions
            await self._process_existing_positions(historical_data, date)
            
            # Look for new entries
            await self._process_new_entries(gap_stocks, historical_data, date)
            
            # Record daily equity
            daily_equity = self.cash + self._calculate_position_value(historical_data, date)
            self.daily_equity.append({
                'date': date.strftime('%Y-%m-%d'),
                'equity': daily_equity
            })
    
    async def _scan_for_gaps(self, historical_data: Dict[str, pd.DataFrame], 
                           date: datetime) -> List[Dict[str, Any]]:
        """Scan for gap-up stocks on given date."""
        gap_stocks = []
        
        for symbol, df in historical_data.items():
            if date not in df.index:
                continue
            
            current_data = df.loc[date]
            
            # Get previous trading day
            prev_dates = df.index[df.index < date]
            if len(prev_dates) == 0:
                continue
            
            prev_date = prev_dates[-1]
            prev_data = df.loc[prev_date]
            
            # Calculate gap
            gap_percent = calculate_gap_percentage(current_data['open'], prev_data['close'])
            
            # Check if meets gap criteria
            if (self.config.trading.min_gap_percent <= gap_percent <= self.config.trading.max_gap_percent and
                self.config.trading.min_price <= current_data['open'] <= self.config.trading.max_price):
                
                gap_stocks.append({
                    'symbol': symbol,
                    'date': date,
                    'current_price': current_data['open'],
                    'previous_close': prev_data['close'],
                    'gap_percent': gap_percent,
                    'gap_fill_level': prev_data['close'],
                    'volume': current_data['volume']
                })
        
        return gap_stocks
    
    async def _process_existing_positions(self, historical_data: Dict[str, pd.DataFrame], date: datetime):
        """Process existing positions for exits."""
        positions_to_close = []
        
        for symbol, position in self.positions.items():
            if symbol not in historical_data or date not in historical_data[symbol].index:
                continue
            
            current_data = historical_data[symbol].loc[date]
            current_price = current_data['close']
            
            # Check exit conditions
            exit_reason = self._check_exit_conditions(position, current_price, date)
            
            if exit_reason:
                positions_to_close.append((symbol, current_price, exit_reason))
        
        # Close positions
        for symbol, exit_price, exit_reason in positions_to_close:
            await self._close_position(symbol, exit_price, exit_reason, date)
    
    def _check_exit_conditions(self, position: Dict[str, Any], current_price: float, date: datetime) -> Optional[str]:
        """Check if position should be closed."""
        
        # Stop loss
        if current_price <= position['stop_loss']:
            return 'stop_loss'
        
        # Profit target
        if current_price >= position['profit_target']:
            return 'profit_target'
        
        # Gap fill
        if current_price <= position['gap_fill_level']:
            return 'gap_fill'
        
        # Time-based exit (end of day)
        hold_time = date - position['entry_time']
        if hold_time.total_seconds() / 3600 >= 6:  # 6 hours max hold
            return 'time_exit'
        
        return None
    
    async def _process_new_entries(self, gap_stocks: List[Dict[str, Any]], 
                                 historical_data: Dict[str, pd.DataFrame], date: datetime):
        """Process new entry opportunities."""
        
        for gap_stock in gap_stocks:
            symbol = gap_stock['symbol']
            
            # Skip if already have position
            if symbol in self.positions:
                continue
            
            # Skip if max positions reached
            if len(self.positions) >= self.config.trading.max_positions:
                break
            
            # Simulate entry logic (simplified)
            entry_price = gap_stock['current_price']
            
            # Calculate position size
            stop_loss = max(gap_stock['gap_fill_level'], entry_price * 0.98)
            position_size = self.risk_manager.calculate_position_size(
                self.cash, self.config.trading.risk_per_trade_percent, entry_price, stop_loss
            )
            
            if position_size > 0:
                await self._enter_position(gap_stock, entry_price, position_size, stop_loss, date)
    
    async def _enter_position(self, gap_stock: Dict[str, Any], entry_price: float, 
                            quantity: int, stop_loss: float, date: datetime):
        """Enter a new position."""
        symbol = gap_stock['symbol']
        
        # Calculate profit target
        profit_target = entry_price + (entry_price - stop_loss) * self.config.trading.profit_target_ratio
        
        # Create position
        position = {
            'symbol': symbol,
            'entry_price': entry_price,
            'quantity': quantity,
            'stop_loss': stop_loss,
            'profit_target': profit_target,
            'gap_fill_level': gap_stock['gap_fill_level'],
            'entry_time': date,
            'gap_percent': gap_stock['gap_percent']
        }
        
        # Update cash
        position_cost = entry_price * quantity
        self.cash -= position_cost
        
        # Add to positions
        self.positions[symbol] = position
        
        self.logger.debug(f"Entered position: {symbol} @ ${entry_price:.2f} ({quantity} shares)")
    
    async def _close_position(self, symbol: str, exit_price: float, exit_reason: str, date: datetime):
        """Close a position."""
        position = self.positions[symbol]
        
        # Calculate P&L
        pnl = (exit_price - position['entry_price']) * position['quantity']
        
        # Update cash
        proceeds = exit_price * position['quantity']
        self.cash += proceeds
        
        # Create trade record
        trade_record = TradeRecord(
            trade_id=f"{symbol}_{date.strftime('%Y%m%d')}",
            symbol=symbol,
            strategy='gap_momentum_backtest',
            entry_price=position['entry_price'],
            exit_price=exit_price,
            quantity=position['quantity'],
            entry_time=position['entry_time'],
            exit_time=date,
            pnl=pnl,
            pnl_percent=(pnl / (position['entry_price'] * position['quantity'])) * 100,
            hold_time_minutes=int((date - position['entry_time']).total_seconds() / 60),
            exit_reason=exit_reason,
            gap_percent=position.get('gap_percent')
        )
        
        self.trades.append(trade_record)
        
        # Remove position
        del self.positions[symbol]
        
        self.logger.debug(f"Closed position: {symbol} @ ${exit_price:.2f} - P&L: ${pnl:.2f} ({exit_reason})")
    
    def _calculate_position_value(self, historical_data: Dict[str, pd.DataFrame] = None, 
                                date: datetime = None) -> float:
        """Calculate current value of all positions."""
        if not self.positions:
            return 0.0
        
        total_value = 0.0
        
        for symbol, position in self.positions.items():
            if historical_data and date and symbol in historical_data:
                if date in historical_data[symbol].index:
                    current_price = historical_data[symbol].loc[date]['close']
                    total_value += current_price * position['quantity']
                else:
                    # Use entry price if no current data
                    total_value += position['entry_price'] * position['quantity']
            else:
                total_value += position['entry_price'] * position['quantity']
        
        return total_value
    
    def _calculate_backtest_stats(self) -> Dict[str, Any]:
        """Calculate comprehensive backtest statistics."""
        if not self.trades:
            return {}
        
        # Use performance tracker to calculate metrics
        metrics = self.performance_tracker.calculate_performance_metrics(self.trades)
        
        # Add backtest-specific metrics
        final_equity = self.cash + self._calculate_position_value()
        total_return = ((final_equity - self.initial_capital) / self.initial_capital) * 100
        
        # Calculate Sharpe ratio from daily returns
        if len(self.daily_equity) > 1:
            daily_returns = []
            for i in range(1, len(self.daily_equity)):
                prev_equity = self.daily_equity[i-1]['equity']
                curr_equity = self.daily_equity[i]['equity']
                daily_return = (curr_equity - prev_equity) / prev_equity
                daily_returns.append(daily_return)
            
            if daily_returns:
                avg_daily_return = np.mean(daily_returns)
                std_daily_return = np.std(daily_returns)
                sharpe_ratio = (avg_daily_return / std_daily_return * np.sqrt(252)) if std_daily_return > 0 else 0
            else:
                sharpe_ratio = 0
        else:
            sharpe_ratio = 0
        
        metrics.update({
            'initial_capital': self.initial_capital,
            'final_equity': final_equity,
            'total_return_percent': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_positions': max(len(self.positions) for _ in [0]) if self.positions else 0
        })
        
        return metrics


class Backtester:
    """
    Main backtester class with optimization capabilities.
    """
    
    def __init__(self, config):
        self.config = config
        self.logger = setup_logger("Backtester", config.logging)
        self.engine = BacktestEngine(config)
    
    async def run_single_backtest(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """Run a single backtest."""
        return await self.engine.run_backtest(start_date, end_date)
    
    async def optimize_parameters(self, start_date: str, end_date: str, 
                                param_ranges: Dict[str, List]) -> Dict[str, Any]:
        """
        Optimize strategy parameters using grid search.
        
        Args:
            start_date: Start date for optimization
            end_date: End date for optimization
            param_ranges: Dictionary of parameter ranges to test
            
        Returns:
            Optimization results
        """
        self.logger.info("Starting parameter optimization...")
        
        best_result = None
        best_params = None
        best_score = float('-inf')
        
        # Generate parameter combinations
        param_combinations = self._generate_param_combinations(param_ranges)
        
        for i, params in enumerate(param_combinations):
            self.logger.info(f"Testing parameter set {i+1}/{len(param_combinations)}: {params}")
            
            # Update config with test parameters
            original_config = self._backup_config()
            self._apply_params_to_config(params)
            
            # Run backtest
            result = await self.engine.run_backtest(start_date, end_date)
            
            # Restore original config
            self._restore_config(original_config)
            
            # Evaluate result (using Sharpe ratio as optimization target)
            if 'statistics' in result:
                score = result['statistics'].get('sharpe_ratio', 0)
                
                if score > best_score:
                    best_score = score
                    best_params = params.copy()
                    best_result = result.copy()
        
        self.logger.info(f"Optimization complete. Best Sharpe ratio: {best_score:.3f}")
        
        return {
            'best_parameters': best_params,
            'best_score': best_score,
            'best_result': best_result,
            'total_combinations_tested': len(param_combinations)
        }
    
    def _generate_param_combinations(self, param_ranges: Dict[str, List]) -> List[Dict[str, Any]]:
        """Generate all parameter combinations for grid search."""
        import itertools
        
        keys = list(param_ranges.keys())
        values = list(param_ranges.values())
        
        combinations = []
        for combination in itertools.product(*values):
            param_dict = dict(zip(keys, combination))
            combinations.append(param_dict)
        
        return combinations
    
    def _backup_config(self) -> Dict[str, Any]:
        """Backup current configuration."""
        return {
            'risk_per_trade_percent': self.config.trading.risk_per_trade_percent,
            'profit_target_ratio': self.config.trading.profit_target_ratio,
            'stop_loss_percent': self.config.trading.stop_loss_percent,
            'min_gap_percent': self.config.trading.min_gap_percent,
            'max_gap_percent': self.config.trading.max_gap_percent
        }
    
    def _apply_params_to_config(self, params: Dict[str, Any]):
        """Apply test parameters to configuration."""
        for param, value in params.items():
            if hasattr(self.config.trading, param):
                setattr(self.config.trading, param, value)
    
    def _restore_config(self, original_config: Dict[str, Any]):
        """Restore original configuration."""
        for param, value in original_config.items():
            if hasattr(self.config.trading, param):
                setattr(self.config.trading, param, value)
