Develop a Python day trading bot for gap-up momentum strategy with these specifications:

STRATEGY OVERVIEW:
- Target: Stocks gapping up 3-8% at market open
- Entry: Pullback to 20 EMA after initial gap surge
- Stop loss: Below gap fill level or -2% from entry
- Profit target: 1.5R (risk-reward ratio)

DATA REQUIREMENTS:
- Real-time market data feed (Alpha Vantage, Polygon.io, or IEX)
- Pre-market gap scanner functionality
- 1-minute and 5-minute OHLCV data
- Volume analysis (compare to 20-day average volume)

PRE-MARKET SCANNING:
1. Identify stocks gapping up 3-8% from previous close
2. Filter by minimum price: $10-$200 per share
3. Require volume > 2x average daily volume
4. Exclude penny stocks and low-float stocks (<10M shares)
5. Check for news catalysts (earnings, FDA approvals, etc.)

ENTRY CONDITIONS:
- Wait for market open + 15 minutes
- Calculate 20 EMA on 5-minute chart
- Enter long when price pulls back to 20 EMA
- Confirm with volume surge (>1.5x average 5-min volume)
- RSI between 40-60 (avoid oversold/overbought)

POSITION MANAGEMENT:
- Risk 1% of account per trade
- Calculate position size based on stop loss distance
- Set stop loss at gap fill level or 2% below entry
- Take profit at 1.5x risk amount
- Trail stop by 0.5 ATR after 1R profit

EXIT RULES:
- Hard stop: 2% loss or gap fill level
- Time stop: Exit by 3:30 PM EST if no profit
- Momentum loss: Exit if price closes below 20 EMA on 5-min chart
- Profit target: 1.5R or resistance level

TECHNICAL INDICATORS NEEDED:
- 20-period EMA
- RSI (14 period)
- Volume moving average (20 period)
- ATR for trailing stops

RISK CONTROLS:
- Maximum 3 concurrent positions
- Daily loss limit: 3% of account
- No trading on FOMC days or major news events
- Minimum $25,000 account size (PDT rule compliance)

PERFORMANCE TRACKING:
- Win/loss ratio tracking
- Average hold time analysis
- Best/worst performing sectors
- Gap size vs success rate correlation

ERROR HANDLING:
- Network connectivity issues
- Broker API rate limits
- Insufficient buying power
- Stock halts or circuit breakers

Include comprehensive logging, paper trading mode, and backtesting capabilities.

My github: HectorTa1989. Show me github readme with some good product names, system architecture in mermaid syntax, workflow in mermaid syntax, Project structure all in github readme. Code for each file in the project structure