#!/usr/bin/env python3
"""
Database utilities for MomentumTsunami trading bot.
"""

import sqlite3
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path
import pandas as pd

from utils.logger import setup_logger


class DatabaseManager:
    """
    Database manager for storing trading data and configuration.
    """
    
    def __init__(self, config):
        self.config = config
        self.logger = setup_logger("DatabaseManager", config.logging)
        
        # Database paths
        self.db_dir = Path("data")
        self.db_dir.mkdir(exist_ok=True)
        
        self.main_db = self.db_dir / "trading.db"
        self.performance_db = self.db_dir / "performance.db"
        
        # Initialize databases
        self._init_databases()
    
    def _init_databases(self):
        """Initialize database schemas."""
        try:
            # Main trading database
            with sqlite3.connect(self.main_db) as conn:
                # Positions table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS positions (
                        position_id TEXT PRIMARY KEY,
                        symbol TEXT NOT NULL,
                        quantity INTEGER NOT NULL,
                        entry_price REAL NOT NULL,
                        current_price REAL,
                        stop_loss REAL,
                        profit_target REAL,
                        entry_time TEXT NOT NULL,
                        strategy TEXT NOT NULL,
                        status TEXT DEFAULT 'OPEN',
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Orders table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS orders (
                        order_id TEXT PRIMARY KEY,
                        symbol TEXT NOT NULL,
                        side TEXT NOT NULL,
                        quantity INTEGER NOT NULL,
                        order_type TEXT NOT NULL,
                        price REAL,
                        stop_price REAL,
                        status TEXT NOT NULL,
                        filled_quantity INTEGER DEFAULT 0,
                        fill_price REAL,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        filled_at TEXT
                    )
                """)
                
                # Gap scans table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS gap_scans (
                        scan_id TEXT PRIMARY KEY,
                        scan_date TEXT NOT NULL,
                        symbol TEXT NOT NULL,
                        gap_percent REAL NOT NULL,
                        current_price REAL NOT NULL,
                        previous_close REAL NOT NULL,
                        volume INTEGER,
                        gap_score REAL,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Configuration table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS config_history (
                        config_id TEXT PRIMARY KEY,
                        config_data TEXT NOT NULL,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                conn.commit()
            
            self.logger.info("Database schemas initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Error initializing databases: {e}")
    
    def save_position(self, position_data: Dict[str, Any]) -> bool:
        """Save position to database."""
        try:
            with sqlite3.connect(self.main_db) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO positions (
                        position_id, symbol, quantity, entry_price, current_price,
                        stop_loss, profit_target, entry_time, strategy, status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    position_data['position_id'],
                    position_data['symbol'],
                    position_data['quantity'],
                    position_data['entry_price'],
                    position_data.get('current_price'),
                    position_data.get('stop_loss'),
                    position_data.get('profit_target'),
                    position_data['entry_time'].isoformat() if isinstance(position_data['entry_time'], datetime) else position_data['entry_time'],
                    position_data.get('strategy', 'gap_momentum'),
                    position_data.get('status', 'OPEN')
                ))
                conn.commit()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving position: {e}")
            return False
    
    def load_positions(self) -> List[Dict[str, Any]]:
        """Load positions from database."""
        try:
            with sqlite3.connect(self.main_db) as conn:
                cursor = conn.execute("""
                    SELECT * FROM positions WHERE status = 'OPEN'
                    ORDER BY created_at DESC
                """)
                
                columns = [description[0] for description in cursor.description]
                positions = []
                
                for row in cursor.fetchall():
                    position = dict(zip(columns, row))
                    positions.append(position)
                
                return positions
                
        except Exception as e:
            self.logger.error(f"Error loading positions: {e}")
            return []
    
    def save_order(self, order_data: Dict[str, Any]) -> bool:
        """Save order to database."""
        try:
            with sqlite3.connect(self.main_db) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO orders (
                        order_id, symbol, side, quantity, order_type, price,
                        stop_price, status, filled_quantity, fill_price,
                        created_at, filled_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    order_data['order_id'],
                    order_data['symbol'],
                    order_data['side'],
                    order_data['quantity'],
                    order_data['order_type'],
                    order_data.get('price'),
                    order_data.get('stop_price'),
                    order_data['status'],
                    order_data.get('filled_quantity', 0),
                    order_data.get('fill_price'),
                    order_data['created_at'].isoformat() if isinstance(order_data['created_at'], datetime) else order_data['created_at'],
                    order_data.get('filled_at')
                ))
                conn.commit()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving order: {e}")
            return False
    
    def save_gap_scan(self, scan_data: Dict[str, Any]) -> bool:
        """Save gap scan results to database."""
        try:
            with sqlite3.connect(self.main_db) as conn:
                for stock in scan_data.get('stocks', []):
                    conn.execute("""
                        INSERT INTO gap_scans (
                            scan_id, scan_date, symbol, gap_percent, current_price,
                            previous_close, volume, gap_score
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        f"{scan_data['scan_date']}_{stock['symbol']}",
                        scan_data['scan_date'],
                        stock['symbol'],
                        stock['gap_percent'],
                        stock['current_price'],
                        stock['previous_close'],
                        stock.get('volume'),
                        stock.get('gap_score')
                    ))
                
                conn.commit()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving gap scan: {e}")
            return False
    
    def get_gap_scan_history(self, days: int = 30) -> pd.DataFrame:
        """Get gap scan history as DataFrame."""
        try:
            with sqlite3.connect(self.main_db) as conn:
                query = """
                    SELECT * FROM gap_scans 
                    WHERE scan_date >= date('now', '-{} days')
                    ORDER BY scan_date DESC, gap_percent DESC
                """.format(days)
                
                df = pd.read_sql_query(query, conn)
                return df
                
        except Exception as e:
            self.logger.error(f"Error getting gap scan history: {e}")
            return pd.DataFrame()
    
    def save_config_snapshot(self, config_data: Dict[str, Any]) -> bool:
        """Save configuration snapshot."""
        try:
            config_id = f"config_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            with sqlite3.connect(self.main_db) as conn:
                conn.execute("""
                    INSERT INTO config_history (config_id, config_data)
                    VALUES (?, ?)
                """, (config_id, json.dumps(config_data, default=str)))
                conn.commit()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving config snapshot: {e}")
            return False
    
    def get_trading_stats(self, days: int = 30) -> Dict[str, Any]:
        """Get trading statistics from database."""
        try:
            with sqlite3.connect(self.main_db) as conn:
                # Get order statistics
                cursor = conn.execute("""
                    SELECT 
                        COUNT(*) as total_orders,
                        SUM(CASE WHEN status = 'filled' THEN 1 ELSE 0 END) as filled_orders,
                        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_orders,
                        SUM(filled_quantity) as total_volume
                    FROM orders 
                    WHERE created_at >= date('now', '-{} days')
                """.format(days))
                
                order_stats = dict(zip([col[0] for col in cursor.description], cursor.fetchone()))
                
                # Get position statistics
                cursor = conn.execute("""
                    SELECT 
                        COUNT(*) as total_positions,
                        COUNT(CASE WHEN status = 'OPEN' THEN 1 END) as open_positions,
                        COUNT(CASE WHEN status = 'CLOSED' THEN 1 END) as closed_positions
                    FROM positions 
                    WHERE created_at >= date('now', '-{} days')
                """.format(days))
                
                position_stats = dict(zip([col[0] for col in cursor.description], cursor.fetchone()))
                
                # Get gap scan statistics
                cursor = conn.execute("""
                    SELECT 
                        COUNT(DISTINCT scan_date) as scan_days,
                        COUNT(*) as total_gaps_found,
                        AVG(gap_percent) as avg_gap_percent,
                        MAX(gap_percent) as max_gap_percent
                    FROM gap_scans 
                    WHERE scan_date >= date('now', '-{} days')
                """.format(days))
                
                gap_stats = dict(zip([col[0] for col in cursor.description], cursor.fetchone()))
                
                return {
                    'period_days': days,
                    'orders': order_stats,
                    'positions': position_stats,
                    'gap_scans': gap_stats,
                    'generated_at': datetime.now().isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"Error getting trading stats: {e}")
            return {}
    
    def cleanup_old_data(self, days_to_keep: int = 90) -> bool:
        """Clean up old data from database."""
        try:
            with sqlite3.connect(self.main_db) as conn:
                # Clean up old gap scans
                conn.execute("""
                    DELETE FROM gap_scans 
                    WHERE scan_date < date('now', '-{} days')
                """.format(days_to_keep))
                
                # Clean up old filled/cancelled orders
                conn.execute("""
                    DELETE FROM orders 
                    WHERE status IN ('filled', 'cancelled') 
                    AND created_at < date('now', '-{} days')
                """.format(days_to_keep))
                
                # Clean up old closed positions
                conn.execute("""
                    DELETE FROM positions 
                    WHERE status = 'CLOSED' 
                    AND created_at < date('now', '-{} days')
                """.format(days_to_keep))
                
                # Clean up old config snapshots (keep last 10)
                conn.execute("""
                    DELETE FROM config_history 
                    WHERE config_id NOT IN (
                        SELECT config_id FROM config_history 
                        ORDER BY created_at DESC LIMIT 10
                    )
                """)
                
                conn.commit()
            
            self.logger.info(f"Cleaned up data older than {days_to_keep} days")
            return True
            
        except Exception as e:
            self.logger.error(f"Error cleaning up old data: {e}")
            return False
    
    def export_data_to_csv(self, table_name: str, filepath: str, days: int = 30) -> bool:
        """Export table data to CSV."""
        try:
            with sqlite3.connect(self.main_db) as conn:
                if table_name == 'gap_scans':
                    query = f"""
                        SELECT * FROM {table_name} 
                        WHERE scan_date >= date('now', '-{days} days')
                        ORDER BY scan_date DESC
                    """
                else:
                    query = f"""
                        SELECT * FROM {table_name} 
                        WHERE created_at >= date('now', '-{days} days')
                        ORDER BY created_at DESC
                    """
                
                df = pd.read_sql_query(query, conn)
                df.to_csv(filepath, index=False)
            
            self.logger.info(f"Exported {table_name} data to {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error exporting data to CSV: {e}")
            return False
    
    def get_database_info(self) -> Dict[str, Any]:
        """Get database information and statistics."""
        try:
            info = {
                'main_db_path': str(self.main_db),
                'performance_db_path': str(self.performance_db),
                'tables': {}
            }
            
            with sqlite3.connect(self.main_db) as conn:
                # Get table information
                cursor = conn.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name NOT LIKE 'sqlite_%'
                """)
                
                tables = [row[0] for row in cursor.fetchall()]
                
                for table in tables:
                    cursor = conn.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    info['tables'][table] = {'row_count': count}
            
            return info
            
        except Exception as e:
            self.logger.error(f"Error getting database info: {e}")
            return {}
