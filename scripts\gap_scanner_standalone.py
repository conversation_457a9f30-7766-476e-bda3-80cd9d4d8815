#!/usr/bin/env python3
"""
Standalone gap scanner for MomentumTsunami trading bot.
Can be run independently to scan for gap-up stocks.
"""

import asyncio
import argparse
import sys
from pathlib import Path
from datetime import datetime

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from config import load_config
from data.data_feed import DataFeed
from data.gap_scanner import GapScanner


async def main():
    parser = argparse.ArgumentParser(description="MomentumTsunami Gap Scanner")
    parser.add_argument("--config", type=str, default="config/settings.yaml",
                       help="Configuration file path")
    parser.add_argument("--output", type=str, help="Output file for results (JSON)")
    parser.add_argument("--min-gap", type=float, help="Minimum gap percentage")
    parser.add_argument("--max-gap", type=float, help="Maximum gap percentage")
    parser.add_argument("--min-price", type=float, help="Minimum stock price")
    parser.add_argument("--max-price", type=float, help="Maximum stock price")
    parser.add_argument("--top", type=int, default=20, help="Show top N results")
    
    args = parser.parse_args()
    
    # Load configuration
    config = load_config(args.config)
    config.debug = True  # Allow scanning outside pre-market
    
    # Override parameters if provided
    if args.min_gap:
        config.trading.min_gap_percent = args.min_gap
    if args.max_gap:
        config.trading.max_gap_percent = args.max_gap
    if args.min_price:
        config.trading.min_price = args.min_price
    if args.max_price:
        config.trading.max_price = args.max_price
    
    print("MomentumTsunami Gap Scanner")
    print("=" * 50)
    print(f"Scan Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Gap Range: {config.trading.min_gap_percent}% - {config.trading.max_gap_percent}%")
    print(f"Price Range: ${config.trading.min_price} - ${config.trading.max_price}")
    print()
    
    # Initialize components
    data_feed = DataFeed(config)
    await data_feed.initialize()
    
    scanner = GapScanner(config, data_feed)
    await scanner.initialize()
    
    try:
        # Run gap scan
        print("Scanning for gap-up stocks...")
        gap_stocks = await scanner.scan_gap_ups()
        
        if not gap_stocks:
            print("No gap-up stocks found matching criteria.")
            return
        
        print(f"Found {len(gap_stocks)} potential gap-up stocks")
        print()
        
        # Filter stocks
        print("Applying filters...")
        filtered_stocks = await scanner.filter_stocks(gap_stocks)
        
        if not filtered_stocks:
            print("No stocks passed filtering criteria.")
            return
        
        print(f"Filtered to {len(filtered_stocks)} high-quality candidates")
        print()
        
        # Display results
        print("Top Gap-Up Stocks:")
        print("-" * 80)
        print(f"{'Symbol':<8} {'Price':<8} {'Gap%':<6} {'Volume':<10} {'Score':<6} {'Reason'}")
        print("-" * 80)
        
        for i, stock in enumerate(filtered_stocks[:args.top]):
            symbol = stock['symbol']
            price = stock['current_price']
            gap_pct = stock['gap_percent']
            volume = stock.get('volume', 0)
            score = stock.get('gap_score', 0)
            
            # Determine reason for inclusion
            reasons = []
            if stock.get('volume_ratio', 0) >= 2.0:
                reasons.append("High Vol")
            if 4.0 <= gap_pct <= 6.0:
                reasons.append("Ideal Gap")
            if stock.get('has_news_catalyst', False):
                reasons.append("News")
            
            reason = ", ".join(reasons) if reasons else "Basic Criteria"
            
            print(f"{symbol:<8} ${price:<7.2f} {gap_pct:<5.1f}% {volume:<10,} {score:<5.2f} {reason}")
        
        # Save results if requested
        if args.output:
            import json
            
            results = {
                'scan_time': datetime.now().isoformat(),
                'scan_parameters': {
                    'min_gap_percent': config.trading.min_gap_percent,
                    'max_gap_percent': config.trading.max_gap_percent,
                    'min_price': config.trading.min_price,
                    'max_price': config.trading.max_price
                },
                'total_found': len(gap_stocks),
                'filtered_count': len(filtered_stocks),
                'stocks': filtered_stocks
            }
            
            with open(args.output, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            print(f"\nResults saved to {args.output}")
        
        # Display summary
        print()
        print("Scan Summary:")
        print(f"- Total stocks scanned: {len(scanner.stock_universe)}")
        print(f"- Gap-up stocks found: {len(gap_stocks)}")
        print(f"- High-quality candidates: {len(filtered_stocks)}")
        
        if filtered_stocks:
            avg_gap = sum(s['gap_percent'] for s in filtered_stocks) / len(filtered_stocks)
            avg_score = sum(s.get('gap_score', 0) for s in filtered_stocks) / len(filtered_stocks)
            print(f"- Average gap: {avg_gap:.1f}%")
            print(f"- Average score: {avg_score:.2f}")
        
    except Exception as e:
        print(f"Error during gap scan: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await data_feed.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
