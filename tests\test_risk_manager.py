#!/usr/bin/env python3
"""
Tests for MomentumTsunami risk management system.
"""

import pytest
from datetime import datetime, timedelta
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from config import Config
from risk.risk_manager import RiskManager, RiskMetrics
from risk.position_manager import PositionManager, Position


class TestRiskManager:
    """Test risk management functionality."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        config = Config()
        config.logging.level = "ERROR"
        config.account.account_size = 25000.0
        config.trading.risk_per_trade_percent = 1.0
        config.trading.max_positions = 3
        config.trading.daily_loss_limit_percent = 3.0
        config.trading.stop_loss_percent = 2.0
        return config
    
    @pytest.fixture
    def risk_manager(self, config):
        """Create risk manager instance."""
        return RiskManager(config)
    
    def test_position_size_calculation(self, risk_manager):
        """Test position size calculation."""
        account_size = 25000.0
        risk_percent = 1.0
        entry_price = 100.0
        stop_loss = 98.0
        
        position_size = risk_manager.calculate_position_size(
            account_size, risk_percent, entry_price, stop_loss
        )
        
        assert position_size > 0
        assert isinstance(position_size, int)
        
        # Risk amount should be approximately 1% of account
        risk_amount = (entry_price - stop_loss) * position_size
        expected_risk = account_size * (risk_percent / 100)
        
        # Allow for some rounding differences
        assert abs(risk_amount - expected_risk) < 50
    
    def test_position_size_invalid_stop(self, risk_manager):
        """Test position size calculation with invalid stop loss."""
        position_size = risk_manager.calculate_position_size(
            25000.0, 1.0, 100.0, 105.0  # Stop above entry
        )
        
        assert position_size == 0
    
    def test_new_position_validation_success(self, risk_manager):
        """Test successful position validation."""
        validation = risk_manager.validate_new_position(
            symbol="TEST",
            entry_price=100.0,
            stop_loss=98.0,
            quantity=100
        )
        
        assert validation['approved'] is True
        assert validation['reason'] == "Position approved"
        assert validation['risk_amount'] > 0
        assert validation['position_value'] > 0
    
    def test_new_position_validation_max_positions(self, risk_manager):
        """Test position validation when max positions reached."""
        # Add positions to reach limit
        for i in range(3):
            risk_manager.add_position(
                symbol=f"TEST{i}",
                entry_price=100.0,
                quantity=100,
                stop_loss=98.0
            )
        
        # Try to add another position
        validation = risk_manager.validate_new_position(
            symbol="TEST4",
            entry_price=100.0,
            stop_loss=98.0,
            quantity=100
        )
        
        assert validation['approved'] is False
        assert "Maximum positions limit reached" in validation['reason']
    
    def test_new_position_validation_duplicate_symbol(self, risk_manager):
        """Test position validation for duplicate symbol."""
        # Add a position
        risk_manager.add_position(
            symbol="TEST",
            entry_price=100.0,
            quantity=100,
            stop_loss=98.0
        )
        
        # Try to add another position with same symbol
        validation = risk_manager.validate_new_position(
            symbol="TEST",
            entry_price=105.0,
            stop_loss=103.0,
            quantity=50
        )
        
        assert validation['approved'] is False
        assert "Already have position in TEST" in validation['reason']
    
    def test_add_position(self, risk_manager):
        """Test adding a position."""
        success = risk_manager.add_position(
            symbol="TEST",
            entry_price=100.0,
            quantity=100,
            stop_loss=98.0
        )
        
        assert success is True
        assert "TEST" in risk_manager.positions
        
        position = risk_manager.positions["TEST"]
        assert position['symbol'] == "TEST"
        assert position['entry_price'] == 100.0
        assert position['quantity'] == 100
        assert position['stop_loss'] == 98.0
        assert position['risk_amount'] == 200.0  # (100 - 98) * 100
    
    def test_update_position(self, risk_manager):
        """Test updating a position."""
        # Add position first
        risk_manager.add_position(
            symbol="TEST",
            entry_price=100.0,
            quantity=100,
            stop_loss=98.0
        )
        
        # Update with current price
        updated_position = risk_manager.update_position("TEST", 105.0)
        
        assert updated_position is not None
        assert updated_position['unrealized_pnl'] == 500.0  # (105 - 100) * 100
        assert updated_position['max_favorable_excursion'] == 500.0
    
    def test_update_position_stop_loss_breach(self, risk_manager):
        """Test position update with stop loss breach."""
        # Add position first
        risk_manager.add_position(
            symbol="TEST",
            entry_price=100.0,
            quantity=100,
            stop_loss=98.0
        )
        
        # Update with price below stop loss
        result = risk_manager.update_position("TEST", 97.0)
        
        assert result is not None
        assert result.get('alert_type') == 'STOP_LOSS_BREACH'
    
    def test_close_position(self, risk_manager):
        """Test closing a position."""
        # Add position first
        risk_manager.add_position(
            symbol="TEST",
            entry_price=100.0,
            quantity=100,
            stop_loss=98.0
        )
        
        # Close position
        trade_record = risk_manager.close_position("TEST", 105.0)
        
        assert trade_record is not None
        assert trade_record['realized_pnl'] == 500.0  # (105 - 100) * 100
        assert "TEST" not in risk_manager.positions
        assert risk_manager.daily_pnl == 500.0
    
    def test_daily_loss_limit_trigger(self, risk_manager):
        """Test daily loss limit circuit breaker."""
        # Set up a large losing position to trigger limit
        risk_manager.daily_pnl = -750.0  # 3% of 25000 = 750
        
        # Try to add another position
        validation = risk_manager.validate_new_position(
            symbol="TEST",
            entry_price=100.0,
            stop_loss=98.0,
            quantity=100
        )
        
        assert validation['approved'] is False
        assert "Daily loss limit reached" in validation['reason']
    
    def test_get_risk_metrics(self, risk_manager):
        """Test risk metrics calculation."""
        # Add some positions
        risk_manager.add_position("TEST1", 100.0, 100, 98.0)
        risk_manager.add_position("TEST2", 50.0, 200, 49.0)
        
        metrics = risk_manager.get_risk_metrics()
        
        assert isinstance(metrics, RiskMetrics)
        assert metrics.current_positions == 2
        assert metrics.total_risk_exposure > 0
        assert metrics.buying_power_used > 0
    
    def test_position_summary(self, risk_manager):
        """Test position summary."""
        # Add positions
        risk_manager.add_position("TEST1", 100.0, 100, 98.0)
        risk_manager.add_position("TEST2", 50.0, 200, 49.0)
        
        # Update with current prices
        risk_manager.update_position("TEST1", 105.0)
        risk_manager.update_position("TEST2", 52.0)
        
        summary = risk_manager.get_position_summary()
        
        assert summary['position_count'] == 2
        assert summary['total_unrealized_pnl'] > 0
        assert summary['total_position_value'] > 0
        assert len(summary['positions']) == 2
    
    def test_daily_stats(self, risk_manager):
        """Test daily statistics calculation."""
        # Add and close some positions
        risk_manager.add_position("TEST1", 100.0, 100, 98.0)
        risk_manager.close_position("TEST1", 105.0)  # Winner
        
        risk_manager.add_position("TEST2", 50.0, 100, 49.0)
        risk_manager.close_position("TEST2", 48.0)  # Loser
        
        stats = risk_manager.get_daily_stats()
        
        assert stats['trades_count'] == 2
        assert stats['winning_trades'] == 1
        assert stats['losing_trades'] == 1
        assert stats['win_rate'] == 50.0
        assert stats['total_pnl'] == 300.0  # 500 - 200
    
    def test_trading_allowed(self, risk_manager):
        """Test trading allowed check."""
        allowed, reason = risk_manager.is_trading_allowed()
        assert allowed is True
        assert reason == "Trading allowed"
        
        # Trigger halt
        risk_manager.trading_halted = True
        risk_manager.halt_reason = "Test halt"
        
        allowed, reason = risk_manager.is_trading_allowed()
        assert allowed is False
        assert reason == "Test halt"
    
    def test_reset_daily_tracking(self, risk_manager):
        """Test daily tracking reset."""
        # Add some data
        risk_manager.daily_pnl = 500.0
        risk_manager.daily_trades = [{'test': 'data'}]
        
        # Reset
        risk_manager.reset_daily_tracking()
        
        assert risk_manager.daily_pnl == 0.0
        assert len(risk_manager.daily_trades) == 0
        assert risk_manager.trading_halted is False


class TestPositionManager:
    """Test position management functionality."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        config = Config()
        config.logging.level = "ERROR"
        return config
    
    @pytest.fixture
    def position_manager(self, config):
        """Create position manager instance."""
        return PositionManager(config)
    
    def test_create_position(self, position_manager):
        """Test position creation."""
        position = position_manager.create_position(
            symbol="TEST",
            entry_price=100.0,
            quantity=100,
            stop_loss=98.0,
            profit_target=103.0
        )
        
        assert isinstance(position, Position)
        assert position.symbol == "TEST"
        assert position.entry_price == 100.0
        assert position.quantity == 100
        assert position.stop_loss == 98.0
        assert position.profit_target == 103.0
        assert position.risk_amount == 200.0
        assert position.position_value == 10000.0
    
    def test_update_position(self, position_manager):
        """Test position update."""
        # Create position
        position_manager.create_position(
            symbol="TEST",
            entry_price=100.0,
            quantity=100,
            stop_loss=98.0,
            profit_target=103.0
        )
        
        # Update position
        updated_position = position_manager.update_position("TEST", 105.0)
        
        assert updated_position is not None
        assert updated_position.current_price == 105.0
        assert updated_position.unrealized_pnl == 500.0
        assert updated_position.max_favorable_excursion == 500.0
    
    def test_close_position(self, position_manager):
        """Test position closing."""
        # Create position
        position_manager.create_position(
            symbol="TEST",
            entry_price=100.0,
            quantity=100,
            stop_loss=98.0,
            profit_target=103.0
        )
        
        # Close position
        closed_position = position_manager.close_position("TEST", 105.0, "profit_target")
        
        assert closed_position is not None
        assert closed_position.status == "CLOSED"
        assert closed_position.unrealized_pnl == 500.0
        assert "TEST" not in position_manager.positions
        assert len(position_manager.closed_positions) == 1
    
    def test_update_stop_loss(self, position_manager):
        """Test stop loss update."""
        # Create position
        position_manager.create_position(
            symbol="TEST",
            entry_price=100.0,
            quantity=100,
            stop_loss=98.0,
            profit_target=103.0
        )
        
        # Update stop loss
        success = position_manager.update_stop_loss("TEST", 99.0)
        
        assert success is True
        position = position_manager.get_position("TEST")
        assert position.stop_loss == 99.0
        assert position.risk_amount == 100.0  # Updated risk
    
    def test_update_trailing_stop(self, position_manager):
        """Test trailing stop update."""
        # Create position
        position_manager.create_position(
            symbol="TEST",
            entry_price=100.0,
            quantity=100,
            stop_loss=98.0,
            profit_target=103.0
        )
        
        # Update trailing stop (higher than current stop)
        success = position_manager.update_trailing_stop("TEST", 99.0)
        
        assert success is True
        position = position_manager.get_position("TEST")
        assert position.stop_loss == 99.0
        assert position.trailing_stop == 99.0
    
    def test_trailing_stop_no_update(self, position_manager):
        """Test trailing stop that shouldn't update."""
        # Create position
        position_manager.create_position(
            symbol="TEST",
            entry_price=100.0,
            quantity=100,
            stop_loss=98.0,
            profit_target=103.0
        )
        
        # Try to update trailing stop lower than current stop
        success = position_manager.update_trailing_stop("TEST", 97.0)
        
        assert success is False
        position = position_manager.get_position("TEST")
        assert position.stop_loss == 98.0  # Unchanged
    
    def test_position_summary(self, position_manager):
        """Test position summary."""
        # Create positions
        position_manager.create_position("TEST1", 100.0, 100, 98.0, 103.0)
        position_manager.create_position("TEST2", 50.0, 200, 49.0, 52.0)
        
        # Update prices
        position_manager.update_position("TEST1", 105.0)
        position_manager.update_position("TEST2", 51.0)
        
        summary = position_manager.get_positions_summary()
        
        assert summary['position_count'] == 2
        assert summary['total_value'] > 0
        assert summary['total_unrealized_pnl'] > 0
        assert len(summary['positions']) == 2
    
    def test_stop_loss_alerts(self, position_manager):
        """Test stop loss breach detection."""
        # Create position
        position_manager.create_position("TEST", 100.0, 100, 98.0, 103.0)
        
        # Update with price below stop loss
        position_manager.update_position("TEST", 97.0)
        
        # Check for alerts
        alerts = position_manager.check_stop_losses()
        
        assert len(alerts) == 1
        assert alerts[0]['symbol'] == "TEST"
        assert alerts[0]['alert_type'] == 'STOP_LOSS_BREACH'
    
    def test_profit_target_alerts(self, position_manager):
        """Test profit target hit detection."""
        # Create position
        position_manager.create_position("TEST", 100.0, 100, 98.0, 103.0)
        
        # Update with price above profit target
        position_manager.update_position("TEST", 104.0)
        
        # Check for alerts
        alerts = position_manager.check_profit_targets()
        
        assert len(alerts) == 1
        assert alerts[0]['symbol'] == "TEST"
        assert alerts[0]['alert_type'] == 'PROFIT_TARGET_HIT'
    
    def test_daily_stats(self, position_manager):
        """Test daily statistics."""
        # Create and close positions
        position_manager.create_position("TEST1", 100.0, 100, 98.0, 103.0)
        position_manager.close_position("TEST1", 105.0)
        
        position_manager.create_position("TEST2", 50.0, 100, 49.0, 52.0)
        position_manager.close_position("TEST2", 48.0)
        
        stats = position_manager.get_daily_stats()
        
        assert stats['positions_opened'] == 2
        assert stats['positions_closed'] == 2
        assert stats['realized_pnl'] == 300.0  # 500 - 200
        assert stats['winning_positions'] == 1
        assert stats['losing_positions'] == 1


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
