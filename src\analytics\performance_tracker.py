#!/usr/bin/env python3
"""
Performance tracking and analytics for MomentumTsunami trading bot.
"""

import json
import sqlite3
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import pandas as pd
import numpy as np
from pathlib import Path

from utils.logger import setup_logger
from utils.helpers import safe_divide


@dataclass
class TradeRecord:
    """Trade record data structure."""
    trade_id: str
    symbol: str
    strategy: str
    entry_price: float
    exit_price: float
    quantity: int
    entry_time: datetime
    exit_time: datetime
    pnl: float
    pnl_percent: float
    hold_time_minutes: int
    exit_reason: str
    gap_percent: Optional[float] = None
    max_favorable_excursion: float = 0.0
    max_adverse_excursion: float = 0.0
    commission: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary with datetime handling."""
        data = asdict(self)
        data['entry_time'] = self.entry_time.isoformat()
        data['exit_time'] = self.exit_time.isoformat()
        return data


@dataclass
class DailyStats:
    """Daily performance statistics."""
    date: str
    trades_count: int
    winning_trades: int
    losing_trades: int
    total_pnl: float
    win_rate: float
    avg_win: float
    avg_loss: float
    largest_win: float
    largest_loss: float
    profit_factor: float
    sharpe_ratio: float
    max_drawdown: float
    total_volume: int


class PerformanceTracker:
    """
    Comprehensive performance tracking and analytics system.
    """
    
    def __init__(self, config):
        self.config = config
        self.logger = setup_logger("PerformanceTracker", config.logging)
        
        # Database setup
        self.db_path = Path("data") / "performance.db"
        self.db_path.parent.mkdir(exist_ok=True)
        
        # In-memory storage for current session
        self.trades: List[TradeRecord] = []
        self.daily_stats: Dict[str, DailyStats] = {}
        
        # Performance metrics
        self.total_pnl = 0.0
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        
        # Initialize database
        self._init_database()
        
        self.logger.info("Performance Tracker initialized")
    
    def _init_database(self):
        """Initialize SQLite database for trade storage."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS trades (
                        trade_id TEXT PRIMARY KEY,
                        symbol TEXT NOT NULL,
                        strategy TEXT NOT NULL,
                        entry_price REAL NOT NULL,
                        exit_price REAL NOT NULL,
                        quantity INTEGER NOT NULL,
                        entry_time TEXT NOT NULL,
                        exit_time TEXT NOT NULL,
                        pnl REAL NOT NULL,
                        pnl_percent REAL NOT NULL,
                        hold_time_minutes INTEGER NOT NULL,
                        exit_reason TEXT NOT NULL,
                        gap_percent REAL,
                        max_favorable_excursion REAL DEFAULT 0,
                        max_adverse_excursion REAL DEFAULT 0,
                        commission REAL DEFAULT 0,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS daily_stats (
                        date TEXT PRIMARY KEY,
                        trades_count INTEGER NOT NULL,
                        winning_trades INTEGER NOT NULL,
                        losing_trades INTEGER NOT NULL,
                        total_pnl REAL NOT NULL,
                        win_rate REAL NOT NULL,
                        avg_win REAL NOT NULL,
                        avg_loss REAL NOT NULL,
                        largest_win REAL NOT NULL,
                        largest_loss REAL NOT NULL,
                        profit_factor REAL NOT NULL,
                        sharpe_ratio REAL NOT NULL,
                        max_drawdown REAL NOT NULL,
                        total_volume INTEGER NOT NULL,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"Error initializing database: {e}")
    
    async def record_trade(self, trade_data: Dict[str, Any]) -> bool:
        """
        Record a completed trade.
        
        Args:
            trade_data: Trade information dictionary
            
        Returns:
            True if recorded successfully
        """
        try:
            # Create trade record
            trade_id = f"{trade_data['symbol']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            hold_time = trade_data['exit_time'] - trade_data['entry_time']
            hold_time_minutes = int(hold_time.total_seconds() / 60)
            
            trade_record = TradeRecord(
                trade_id=trade_id,
                symbol=trade_data['symbol'],
                strategy=trade_data.get('strategy', 'gap_momentum'),
                entry_price=trade_data['entry_price'],
                exit_price=trade_data['exit_price'],
                quantity=trade_data['quantity'],
                entry_time=trade_data['entry_time'],
                exit_time=trade_data['exit_time'],
                pnl=trade_data['pnl'],
                pnl_percent=(trade_data['pnl'] / (trade_data['entry_price'] * trade_data['quantity'])) * 100,
                hold_time_minutes=hold_time_minutes,
                exit_reason=trade_data.get('exit_reason', 'unknown'),
                gap_percent=trade_data.get('gap_percent'),
                max_favorable_excursion=trade_data.get('max_favorable_excursion', 0.0),
                max_adverse_excursion=trade_data.get('max_adverse_excursion', 0.0),
                commission=trade_data.get('commission', 0.0)
            )
            
            # Add to in-memory storage
            self.trades.append(trade_record)
            
            # Update counters
            self.total_trades += 1
            self.total_pnl += trade_record.pnl
            
            if trade_record.pnl > 0:
                self.winning_trades += 1
            else:
                self.losing_trades += 1
            
            # Save to database
            await self._save_trade_to_db(trade_record)
            
            self.logger.info(f"Trade recorded: {trade_id} - P&L: ${trade_record.pnl:.2f}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error recording trade: {e}")
            return False
    
    async def _save_trade_to_db(self, trade: TradeRecord):
        """Save trade record to database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO trades (
                        trade_id, symbol, strategy, entry_price, exit_price, quantity,
                        entry_time, exit_time, pnl, pnl_percent, hold_time_minutes,
                        exit_reason, gap_percent, max_favorable_excursion,
                        max_adverse_excursion, commission
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    trade.trade_id, trade.symbol, trade.strategy, trade.entry_price,
                    trade.exit_price, trade.quantity, trade.entry_time.isoformat(),
                    trade.exit_time.isoformat(), trade.pnl, trade.pnl_percent,
                    trade.hold_time_minutes, trade.exit_reason, trade.gap_percent,
                    trade.max_favorable_excursion, trade.max_adverse_excursion,
                    trade.commission
                ))
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"Error saving trade to database: {e}")
    
    def calculate_performance_metrics(self, trades: List[TradeRecord] = None) -> Dict[str, Any]:
        """
        Calculate comprehensive performance metrics.
        
        Args:
            trades: List of trades to analyze (defaults to all trades)
            
        Returns:
            Performance metrics dictionary
        """
        if trades is None:
            trades = self.trades
        
        if not trades:
            return self._empty_metrics()
        
        # Basic metrics
        total_trades = len(trades)
        winning_trades = [t for t in trades if t.pnl > 0]
        losing_trades = [t for t in trades if t.pnl < 0]
        
        total_pnl = sum(t.pnl for t in trades)
        win_rate = len(winning_trades) / total_trades * 100
        
        # Win/Loss metrics
        avg_win = sum(t.pnl for t in winning_trades) / len(winning_trades) if winning_trades else 0
        avg_loss = sum(t.pnl for t in losing_trades) / len(losing_trades) if losing_trades else 0
        largest_win = max((t.pnl for t in winning_trades), default=0)
        largest_loss = min((t.pnl for t in losing_trades), default=0)
        
        # Risk metrics
        profit_factor = safe_divide(abs(sum(t.pnl for t in winning_trades)), 
                                  abs(sum(t.pnl for t in losing_trades)), 0)
        
        # Calculate Sharpe ratio (simplified)
        returns = [t.pnl_percent for t in trades]
        avg_return = np.mean(returns) if returns else 0
        std_return = np.std(returns) if len(returns) > 1 else 0
        sharpe_ratio = safe_divide(avg_return, std_return, 0) if std_return > 0 else 0
        
        # Calculate maximum drawdown
        max_drawdown = self._calculate_max_drawdown(trades)
        
        # Hold time analysis
        hold_times = [t.hold_time_minutes for t in trades]
        avg_hold_time = np.mean(hold_times) if hold_times else 0
        
        # Volume metrics
        total_volume = sum(t.quantity for t in trades)
        
        return {
            'total_trades': total_trades,
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'largest_win': largest_win,
            'largest_loss': largest_loss,
            'profit_factor': profit_factor,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'avg_hold_time_minutes': avg_hold_time,
            'total_volume': total_volume,
            'avg_pnl_per_trade': total_pnl / total_trades if total_trades > 0 else 0
        }
    
    def _calculate_max_drawdown(self, trades: List[TradeRecord]) -> float:
        """Calculate maximum drawdown from trade sequence."""
        if not trades:
            return 0.0
        
        # Sort trades by exit time
        sorted_trades = sorted(trades, key=lambda t: t.exit_time)
        
        # Calculate cumulative P&L
        cumulative_pnl = []
        running_total = 0
        
        for trade in sorted_trades:
            running_total += trade.pnl
            cumulative_pnl.append(running_total)
        
        # Calculate drawdown
        peak = cumulative_pnl[0]
        max_drawdown = 0
        
        for pnl in cumulative_pnl:
            if pnl > peak:
                peak = pnl
            
            drawdown = peak - pnl
            if drawdown > max_drawdown:
                max_drawdown = drawdown
        
        return max_drawdown
    
    def _empty_metrics(self) -> Dict[str, Any]:
        """Return empty metrics dictionary."""
        return {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'win_rate': 0.0,
            'total_pnl': 0.0,
            'avg_win': 0.0,
            'avg_loss': 0.0,
            'largest_win': 0.0,
            'largest_loss': 0.0,
            'profit_factor': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown': 0.0,
            'avg_hold_time_minutes': 0.0,
            'total_volume': 0,
            'avg_pnl_per_trade': 0.0
        }
    
    async def get_daily_stats(self, date: str = None) -> Dict[str, Any]:
        """
        Get daily performance statistics.
        
        Args:
            date: Date string (YYYY-MM-DD), defaults to today
            
        Returns:
            Daily statistics dictionary
        """
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
        
        # Filter trades for the specific date
        daily_trades = [t for t in self.trades if t.exit_time.strftime('%Y-%m-%d') == date]
        
        return self.calculate_performance_metrics(daily_trades)
    
    def get_symbol_performance(self, symbol: str) -> Dict[str, Any]:
        """Get performance metrics for a specific symbol."""
        symbol_trades = [t for t in self.trades if t.symbol == symbol]
        return self.calculate_performance_metrics(symbol_trades)
    
    def get_strategy_performance(self, strategy: str) -> Dict[str, Any]:
        """Get performance metrics for a specific strategy."""
        strategy_trades = [t for t in self.trades if t.strategy == strategy]
        return self.calculate_performance_metrics(strategy_trades)
    
    def get_gap_analysis(self) -> Dict[str, Any]:
        """Analyze performance by gap size."""
        gap_trades = [t for t in self.trades if t.gap_percent is not None]
        
        if not gap_trades:
            return {'message': 'No gap data available'}
        
        # Group by gap ranges
        gap_ranges = {
            '3-4%': [t for t in gap_trades if 3 <= t.gap_percent < 4],
            '4-5%': [t for t in gap_trades if 4 <= t.gap_percent < 5],
            '5-6%': [t for t in gap_trades if 5 <= t.gap_percent < 6],
            '6-7%': [t for t in gap_trades if 6 <= t.gap_percent < 7],
            '7-8%': [t for t in gap_trades if 7 <= t.gap_percent <= 8],
            '>8%': [t for t in gap_trades if t.gap_percent > 8]
        }
        
        analysis = {}
        for range_name, trades in gap_ranges.items():
            if trades:
                metrics = self.calculate_performance_metrics(trades)
                analysis[range_name] = {
                    'trade_count': len(trades),
                    'win_rate': metrics['win_rate'],
                    'avg_pnl': metrics['avg_pnl_per_trade'],
                    'total_pnl': metrics['total_pnl']
                }
        
        return analysis
    
    def export_trades_to_csv(self, filepath: str, start_date: str = None, end_date: str = None) -> bool:
        """
        Export trades to CSV file.
        
        Args:
            filepath: Output file path
            start_date: Start date filter (YYYY-MM-DD)
            end_date: End date filter (YYYY-MM-DD)
            
        Returns:
            True if exported successfully
        """
        try:
            trades_to_export = self.trades
            
            # Apply date filters
            if start_date:
                start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                trades_to_export = [t for t in trades_to_export if t.exit_time >= start_dt]
            
            if end_date:
                end_dt = datetime.strptime(end_date, '%Y-%m-%d')
                trades_to_export = [t for t in trades_to_export if t.exit_time <= end_dt]
            
            # Convert to DataFrame
            df = pd.DataFrame([t.to_dict() for t in trades_to_export])
            
            # Save to CSV
            df.to_csv(filepath, index=False)
            
            self.logger.info(f"Exported {len(trades_to_export)} trades to {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error exporting trades to CSV: {e}")
            return False
    
    def generate_performance_report(self) -> str:
        """Generate a comprehensive performance report."""
        metrics = self.calculate_performance_metrics()
        
        report = f"""
=== MomentumTsunami Performance Report ===
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

OVERALL PERFORMANCE:
- Total Trades: {metrics['total_trades']}
- Win Rate: {metrics['win_rate']:.1f}%
- Total P&L: ${metrics['total_pnl']:,.2f}
- Average P&L per Trade: ${metrics['avg_pnl_per_trade']:,.2f}

WIN/LOSS ANALYSIS:
- Winning Trades: {metrics['winning_trades']}
- Losing Trades: {metrics['losing_trades']}
- Average Win: ${metrics['avg_win']:,.2f}
- Average Loss: ${metrics['avg_loss']:,.2f}
- Largest Win: ${metrics['largest_win']:,.2f}
- Largest Loss: ${metrics['largest_loss']:,.2f}

RISK METRICS:
- Profit Factor: {metrics['profit_factor']:.2f}
- Sharpe Ratio: {metrics['sharpe_ratio']:.2f}
- Maximum Drawdown: ${metrics['max_drawdown']:,.2f}

TRADING METRICS:
- Average Hold Time: {metrics['avg_hold_time_minutes']:.1f} minutes
- Total Volume: {metrics['total_volume']:,} shares
"""
        
        return report
