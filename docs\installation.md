# MomentumTsunami Installation Guide

## System Requirements

### Minimum Requirements
- **Python**: 3.8 or higher
- **Operating System**: Windows 10/11, macOS 10.14+, or Linux (Ubuntu 18.04+)
- **Memory**: 4GB RAM minimum, 8GB recommended
- **Storage**: 2GB free disk space
- **Network**: Stable internet connection for market data

### Recommended Requirements
- **Python**: 3.10 or higher
- **Memory**: 16GB RAM for optimal performance
- **Storage**: 10GB free disk space (for historical data and logs)
- **Account Size**: $25,000+ for live trading (PDT rule compliance)

## Installation Steps

### 1. Clone the Repository

```bash
git clone https://github.com/HectorTa1989/MomentumTsunami.git
cd MomentumTsunami
```

### 2. Create Virtual Environment

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate

# On macOS/Linux:
source venv/bin/activate
```

### 3. Install Dependencies

```bash
# Install required packages
pip install -r requirements.txt

# Install TA-Lib (technical analysis library)
# On Windows:
pip install TA-Lib

# On macOS:
brew install ta-lib
pip install TA-Lib

# On Linux:
sudo apt-get install libta-lib-dev
pip install TA-Lib
```

### 4. Install the Package

```bash
# Install in development mode
pip install -e .
```

## Configuration Setup

### 1. Create Configuration File

```bash
# Copy example configuration
cp config/settings.yaml.example config/settings.yaml
```

### 2. Set Environment Variables

Create a `.env` file in the project root:

```bash
# Data Provider API Keys
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
POLYGON_API_KEY=your_polygon_key
IEX_TOKEN=your_iex_token

# Broker API Credentials (for live trading)
BROKER_API_KEY=your_broker_api_key
BROKER_SECRET=your_broker_secret

# Account Settings
ACCOUNT_SIZE=25000
PAPER_TRADING=true
DEBUG=false
```

### 3. Configure Trading Parameters

Edit `config/settings.yaml`:

```yaml
trading:
  # Gap scanning parameters
  min_gap_percent: 3.0
  max_gap_percent: 8.0
  min_price: 10.0
  max_price: 200.0
  
  # Risk management
  risk_per_trade_percent: 1.0
  max_positions: 3
  daily_loss_limit_percent: 3.0
  
  # Strategy parameters
  ema_period: 20
  rsi_period: 14
  profit_target_ratio: 1.5
```

## API Key Setup

### Alpha Vantage (Free Tier Available)
1. Visit [Alpha Vantage](https://www.alphavantage.co/support/#api-key)
2. Sign up for a free API key
3. Add to environment variables

### Polygon.io (Paid Service)
1. Visit [Polygon.io](https://polygon.io/)
2. Subscribe to a plan with real-time data
3. Get your API key from the dashboard
4. Add to environment variables

### IEX Cloud (Free Tier Available)
1. Visit [IEX Cloud](https://iexcloud.io/)
2. Sign up for an account
3. Get your publishable token
4. Add to environment variables

## Database Setup

The bot uses SQLite by default (no additional setup required). For production, you may want to configure PostgreSQL:

```bash
# Install PostgreSQL dependencies
pip install psycopg2-binary

# Update database configuration in settings.yaml
database:
  type: postgresql
  host: localhost
  port: 5432
  name: momentum_tsunami
  user: your_username
  password: your_password
```

## Verification

### 1. Run Tests

```bash
# Run all tests
pytest tests/ -v

# Run specific test modules
pytest tests/test_strategy.py -v
pytest tests/test_risk_manager.py -v
pytest tests/test_data_feed.py -v
```

### 2. Test Configuration

```bash
# Test configuration loading
python -c "from config import load_config; print('Config loaded successfully')"

# Test data feed connection
python src/data/gap_scanner.py
```

### 3. Run Paper Trading Mode

```bash
# Start in paper trading mode
python src/main.py --mode paper --debug
```

## Troubleshooting

### Common Issues

#### TA-Lib Installation Issues
```bash
# On Windows, if TA-Lib fails to install:
# Download the appropriate wheel from:
# https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib
pip install TA_Lib-0.4.25-cp310-cp310-win_amd64.whl
```

#### Permission Errors
```bash
# On Linux/macOS, if you get permission errors:
sudo chown -R $USER:$USER /path/to/MomentumTsunami
chmod +x src/main.py
```

#### API Connection Issues
- Verify API keys are correct
- Check internet connection
- Ensure API rate limits aren't exceeded
- Try switching to backup data providers

#### Memory Issues
- Reduce the number of stocks in the universe
- Decrease historical data period
- Increase system swap space

### Log Files

Check log files for detailed error information:
```bash
# View main log
tail -f data/logs/momentumtsunami.log

# View error log
tail -f data/logs/momentumtsunami_errors.log

# View trade log
tail -f data/logs/trades.log
```

## Performance Optimization

### 1. System Optimization
- Use SSD storage for better I/O performance
- Ensure adequate RAM (16GB+ recommended)
- Close unnecessary applications during trading hours

### 2. Configuration Optimization
- Reduce scan universe size for faster processing
- Adjust data update intervals based on your needs
- Enable caching for frequently accessed data

### 3. Network Optimization
- Use wired internet connection when possible
- Consider VPS hosting near market data centers
- Monitor API rate limits and usage

## Security Considerations

### 1. API Key Security
- Never commit API keys to version control
- Use environment variables or secure key management
- Rotate API keys regularly

### 2. Trading Security
- Start with paper trading mode
- Use small position sizes initially
- Implement proper risk management
- Monitor trades closely

### 3. System Security
- Keep system and dependencies updated
- Use firewall protection
- Regular backups of configuration and data

## Next Steps

After successful installation:

1. **Read the Configuration Guide**: [configuration.md](configuration.md)
2. **Review the API Reference**: [api_reference.md](api_reference.md)
3. **Start with Paper Trading**: Test the system thoroughly
4. **Backtest Your Strategy**: Validate performance on historical data
5. **Gradual Live Trading**: Start with small positions

## Support

For installation issues:
- Check the [GitHub Issues](https://github.com/HectorTa1989/MomentumTsunami/issues)
- Review the troubleshooting section above
- Ensure all dependencies are properly installed
- Verify API keys and configuration settings

Remember: Always test thoroughly in paper trading mode before risking real capital!
