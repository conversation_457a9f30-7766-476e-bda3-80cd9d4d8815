#!/usr/bin/env python3
"""
Technical indicators for MomentumTsunami trading bot.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import talib
from utils.logger import setup_logger


class TechnicalIndicators:
    """
    Technical indicators calculator for trading strategies.
    """
    
    def __init__(self, config):
        self.config = config
        self.logger = setup_logger("TechnicalIndicators", config.logging)
    
    def calculate_ema(self, prices: pd.Series, period: int) -> pd.Series:
        """
        Calculate Exponential Moving Average.
        
        Args:
            prices: Price series
            period: EMA period
            
        Returns:
            EMA values
        """
        return talib.EMA(prices.values, timeperiod=period)
    
    def calculate_sma(self, prices: pd.Series, period: int) -> pd.Series:
        """
        Calculate Simple Moving Average.
        
        Args:
            prices: Price series
            period: SMA period
            
        Returns:
            SMA values
        """
        return talib.SMA(prices.values, timeperiod=period)
    
    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """
        Calculate Relative Strength Index.
        
        Args:
            prices: Price series
            period: RSI period
            
        Returns:
            RSI values
        """
        return talib.RSI(prices.values, timeperiod=period)
    
    def calculate_atr(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """
        Calculate Average True Range.
        
        Args:
            high: High prices
            low: Low prices
            close: Close prices
            period: ATR period
            
        Returns:
            ATR values
        """
        return talib.ATR(high.values, low.values, close.values, timeperiod=period)
    
    def calculate_bollinger_bands(self, prices: pd.Series, period: int = 20, std_dev: float = 2.0) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        Calculate Bollinger Bands.
        
        Args:
            prices: Price series
            period: Moving average period
            std_dev: Standard deviation multiplier
            
        Returns:
            Tuple of (upper_band, middle_band, lower_band)
        """
        upper, middle, lower = talib.BBANDS(prices.values, timeperiod=period, nbdevup=std_dev, nbdevdn=std_dev)
        return upper, middle, lower
    
    def calculate_macd(self, prices: pd.Series, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        Calculate MACD (Moving Average Convergence Divergence).
        
        Args:
            prices: Price series
            fast_period: Fast EMA period
            slow_period: Slow EMA period
            signal_period: Signal line EMA period
            
        Returns:
            Tuple of (macd_line, signal_line, histogram)
        """
        macd, signal, histogram = talib.MACD(prices.values, fastperiod=fast_period, slowperiod=slow_period, signalperiod=signal_period)
        return macd, signal, histogram
    
    def calculate_stochastic(self, high: pd.Series, low: pd.Series, close: pd.Series, k_period: int = 14, d_period: int = 3) -> Tuple[pd.Series, pd.Series]:
        """
        Calculate Stochastic Oscillator.
        
        Args:
            high: High prices
            low: Low prices
            close: Close prices
            k_period: %K period
            d_period: %D period
            
        Returns:
            Tuple of (%K, %D)
        """
        k_percent, d_percent = talib.STOCH(high.values, low.values, close.values, fastk_period=k_period, slowk_period=d_period, slowd_period=d_period)
        return k_percent, d_percent
    
    def calculate_volume_sma(self, volume: pd.Series, period: int = 20) -> pd.Series:
        """
        Calculate Volume Simple Moving Average.
        
        Args:
            volume: Volume series
            period: SMA period
            
        Returns:
            Volume SMA values
        """
        return talib.SMA(volume.values, timeperiod=period)
    
    def calculate_vwap(self, high: pd.Series, low: pd.Series, close: pd.Series, volume: pd.Series) -> pd.Series:
        """
        Calculate Volume Weighted Average Price.
        
        Args:
            high: High prices
            low: Low prices
            close: Close prices
            volume: Volume series
            
        Returns:
            VWAP values
        """
        typical_price = (high + low + close) / 3
        vwap = (typical_price * volume).cumsum() / volume.cumsum()
        return vwap
    
    def calculate_support_resistance(self, high: pd.Series, low: pd.Series, close: pd.Series, window: int = 20) -> Dict[str, float]:
        """
        Calculate support and resistance levels.
        
        Args:
            high: High prices
            low: Low prices
            close: Close prices
            window: Lookback window
            
        Returns:
            Dictionary with support and resistance levels
        """
        recent_data = close.tail(window)
        
        # Simple support/resistance calculation
        resistance = high.tail(window).max()
        support = low.tail(window).min()
        
        # Pivot points
        pivot = (high.iloc[-1] + low.iloc[-1] + close.iloc[-1]) / 3
        
        return {
            'support': support,
            'resistance': resistance,
            'pivot': pivot,
            'current_price': close.iloc[-1]
        }
    
    def calculate_momentum_indicators(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """
        Calculate multiple momentum indicators.
        
        Args:
            data: OHLCV DataFrame
            
        Returns:
            Dictionary of momentum indicators
        """
        indicators = {}
        
        # Price-based indicators
        indicators['ema_20'] = self.calculate_ema(data['close'], 20)
        indicators['ema_50'] = self.calculate_ema(data['close'], 50)
        indicators['sma_20'] = self.calculate_sma(data['close'], 20)
        indicators['rsi'] = self.calculate_rsi(data['close'], 14)
        
        # Volatility indicators
        indicators['atr'] = self.calculate_atr(data['high'], data['low'], data['close'], 14)
        
        # Bollinger Bands
        bb_upper, bb_middle, bb_lower = self.calculate_bollinger_bands(data['close'])
        indicators['bb_upper'] = bb_upper
        indicators['bb_middle'] = bb_middle
        indicators['bb_lower'] = bb_lower
        
        # MACD
        macd, signal, histogram = self.calculate_macd(data['close'])
        indicators['macd'] = macd
        indicators['macd_signal'] = signal
        indicators['macd_histogram'] = histogram
        
        # Volume indicators
        indicators['volume_sma'] = self.calculate_volume_sma(data['volume'], 20)
        indicators['vwap'] = self.calculate_vwap(data['high'], data['low'], data['close'], data['volume'])
        
        return indicators
    
    def is_bullish_momentum(self, data: pd.DataFrame, indicators: Dict[str, pd.Series]) -> bool:
        """
        Check if current momentum is bullish.
        
        Args:
            data: OHLCV DataFrame
            indicators: Technical indicators
            
        Returns:
            True if bullish momentum detected
        """
        current_price = data['close'].iloc[-1]
        
        # Check EMA alignment
        ema_20 = indicators['ema_20'][-1]
        ema_50 = indicators['ema_50'][-1]
        
        if pd.isna(ema_20) or pd.isna(ema_50):
            return False
        
        # Price above EMAs and EMAs in bullish order
        price_above_emas = current_price > ema_20 > ema_50
        
        # RSI in favorable range (not overbought)
        rsi = indicators['rsi'][-1]
        rsi_favorable = 40 <= rsi <= 70 if not pd.isna(rsi) else True
        
        # MACD bullish
        macd = indicators['macd'][-1]
        macd_signal = indicators['macd_signal'][-1]
        macd_bullish = macd > macd_signal if not (pd.isna(macd) or pd.isna(macd_signal)) else True
        
        # Volume above average
        current_volume = data['volume'].iloc[-1]
        avg_volume = indicators['volume_sma'][-1]
        volume_strong = current_volume > avg_volume if not pd.isna(avg_volume) else True
        
        return price_above_emas and rsi_favorable and macd_bullish and volume_strong
    
    def is_bearish_momentum(self, data: pd.DataFrame, indicators: Dict[str, pd.Series]) -> bool:
        """
        Check if current momentum is bearish.
        
        Args:
            data: OHLCV DataFrame
            indicators: Technical indicators
            
        Returns:
            True if bearish momentum detected
        """
        current_price = data['close'].iloc[-1]
        
        # Check EMA alignment
        ema_20 = indicators['ema_20'][-1]
        ema_50 = indicators['ema_50'][-1]
        
        if pd.isna(ema_20) or pd.isna(ema_50):
            return False
        
        # Price below EMAs and EMAs in bearish order
        price_below_emas = current_price < ema_20 < ema_50
        
        # RSI in unfavorable range (oversold or overbought)
        rsi = indicators['rsi'][-1]
        rsi_unfavorable = rsi < 30 or rsi > 70 if not pd.isna(rsi) else False
        
        # MACD bearish
        macd = indicators['macd'][-1]
        macd_signal = indicators['macd_signal'][-1]
        macd_bearish = macd < macd_signal if not (pd.isna(macd) or pd.isna(macd_signal)) else False
        
        return price_below_emas or rsi_unfavorable or macd_bearish
    
    def calculate_entry_score(self, data: pd.DataFrame, indicators: Dict[str, pd.Series]) -> float:
        """
        Calculate entry score based on technical indicators.
        
        Args:
            data: OHLCV DataFrame
            indicators: Technical indicators
            
        Returns:
            Entry score between 0 and 1
        """
        score = 0.0
        current_price = data['close'].iloc[-1]
        
        # EMA alignment score (30% weight)
        ema_20 = indicators['ema_20'][-1]
        if not pd.isna(ema_20):
            if abs(current_price - ema_20) / current_price < 0.02:  # Within 2% of EMA
                score += 0.3
            elif current_price > ema_20:
                score += 0.15
        
        # RSI score (20% weight)
        rsi = indicators['rsi'][-1]
        if not pd.isna(rsi):
            if 40 <= rsi <= 60:
                score += 0.2
            elif 30 <= rsi <= 70:
                score += 0.1
        
        # Volume score (20% weight)
        current_volume = data['volume'].iloc[-1]
        avg_volume = indicators['volume_sma'][-1]
        if not pd.isna(avg_volume) and avg_volume > 0:
            volume_ratio = current_volume / avg_volume
            if volume_ratio >= 1.5:
                score += 0.2
            elif volume_ratio >= 1.0:
                score += 0.1
        
        # MACD score (15% weight)
        macd = indicators['macd'][-1]
        macd_signal = indicators['macd_signal'][-1]
        if not (pd.isna(macd) or pd.isna(macd_signal)):
            if macd > macd_signal:
                score += 0.15
        
        # ATR score (15% weight) - prefer moderate volatility
        atr = indicators['atr'][-1]
        if not pd.isna(atr):
            atr_percent = (atr / current_price) * 100
            if 1 <= atr_percent <= 4:  # 1-4% daily range
                score += 0.15
            elif 0.5 <= atr_percent <= 6:
                score += 0.075
        
        return min(score, 1.0)
    
    def get_trailing_stop_level(self, entry_price: float, current_price: float, atr: float, multiplier: float = 0.5) -> float:
        """
        Calculate trailing stop level based on ATR.
        
        Args:
            entry_price: Original entry price
            current_price: Current market price
            atr: Average True Range value
            multiplier: ATR multiplier for stop distance
            
        Returns:
            Trailing stop level
        """
        if pd.isna(atr) or atr <= 0:
            # Fallback to percentage-based stop
            return current_price * 0.98  # 2% stop
        
        stop_distance = atr * multiplier
        trailing_stop = current_price - stop_distance
        
        # Ensure stop is not below entry (for long positions)
        min_stop = entry_price * 0.98  # Never risk more than 2%
        
        return max(trailing_stop, min_stop)
