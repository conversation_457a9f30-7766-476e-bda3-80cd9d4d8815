# 🌊 MomentumTsunami - Advanced Gap-Up Day Trading Bot

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Status](https://img.shields.io/badge/Status-Active%20Development-orange.svg)]()

## 🚀 Product Overview

**MomentumTsunami** is a sophisticated Python-based day trading bot designed to capitalize on gap-up momentum strategies. The system automatically identifies stocks gapping up 3-8% at market open, waits for pullbacks to the 20 EMA, and executes trades with precise risk management.

### Alternative Product Names
- **GapRider Pro** - Professional gap trading system
- **MomentumMaster** - Advanced momentum trading platform  
- **TsunamiTrader** - High-frequency gap momentum bot
- **GapMomentum Elite** - Premium day trading solution

## 🏗️ System Architecture

```mermaid
graph TB
    subgraph "Data Layer"
        A[Alpha Vantage API] --> D[Data Aggregator]
        B[Polygon.io API] --> D
        C[IEX Cloud API] --> D
        D --> E[Real-time Data Stream]
    end
    
    subgraph "Processing Layer"
        E --> F[Gap Scanner]
        E --> G[Technical Indicators]
        F --> H[Stock Filter]
        G --> I[Signal Generator]
    end
    
    subgraph "Strategy Layer"
        H --> J[Entry Logic]
        I --> J
        J --> K[Position Manager]
        K --> L[Risk Manager]
    end
    
    subgraph "Execution Layer"
        L --> M[Order Manager]
        M --> N[Broker API]
        N --> O[Trade Execution]
    end
    
    subgraph "Monitoring Layer"
        O --> P[Performance Tracker]
        P --> Q[Logger]
        Q --> R[Dashboard]
    end
    
    subgraph "Storage Layer"
        S[(Trade Database)]
        T[(Config Database)]
        U[(Performance Database)]
    end
    
    P --> S
    K --> T
    Q --> U
```

## 🔄 Trading Workflow

```mermaid
flowchart TD
    A[Market Pre-Open] --> B[Scan for Gap-Up Stocks]
    B --> C{Gap 3-8%?}
    C -->|No| B
    C -->|Yes| D[Apply Filters]
    D --> E{Pass All Filters?}
    E -->|No| B
    E -->|Yes| F[Add to Watchlist]
    
    F --> G[Market Open + 15min]
    G --> H[Monitor for Pullback]
    H --> I{Price at 20 EMA?}
    I -->|No| H
    I -->|Yes| J[Check Entry Conditions]
    
    J --> K{All Conditions Met?}
    K -->|No| H
    K -->|Yes| L[Calculate Position Size]
    L --> M[Place Entry Order]
    
    M --> N[Monitor Position]
    N --> O{Exit Condition?}
    O -->|No| N
    O -->|Yes| P[Close Position]
    
    P --> Q[Update Performance]
    Q --> R[Log Trade Results]
    R --> S[End of Day Analysis]
```

## 📁 Project Structure

```
MomentumTsunami/
├── README.md
├── requirements.txt
├── setup.py
├── config/
│   ├── __init__.py
│   ├── config.py
│   └── settings.yaml
├── src/
│   ├── __init__.py
│   ├── main.py
│   ├── data/
│   │   ├── __init__.py
│   │   ├── data_feed.py
│   │   ├── gap_scanner.py
│   │   └── market_data.py
│   ├── strategy/
│   │   ├── __init__.py
│   │   ├── indicators.py
│   │   ├── strategy.py
│   │   └── signals.py
│   ├── risk/
│   │   ├── __init__.py
│   │   ├── risk_manager.py
│   │   └── position_manager.py
│   ├── execution/
│   │   ├── __init__.py
│   │   ├── order_manager.py
│   │   └── broker_interface.py
│   ├── analytics/
│   │   ├── __init__.py
│   │   ├── performance_tracker.py
│   │   └── backtester.py
│   └── utils/
│       ├── __init__.py
│       ├── logger.py
│       ├── database.py
│       └── helpers.py
├── tests/
│   ├── __init__.py
│   ├── test_strategy.py
│   ├── test_risk_manager.py
│   └── test_data_feed.py
├── data/
│   ├── historical/
│   ├── logs/
│   └── backtest_results/
└── docs/
    ├── installation.md
    ├── configuration.md
    └── api_reference.md
```

## ⚡ Key Features

### 🎯 Strategy Specifications
- **Target**: Stocks gapping up 3-8% at market open
- **Entry**: Pullback to 20 EMA after initial gap surge  
- **Stop Loss**: Below gap fill level or -2% from entry
- **Profit Target**: 1.5R (risk-reward ratio)
- **Risk Management**: 1% account risk per trade

### 📊 Pre-Market Scanning
- Gap detection: 3-8% from previous close
- Price filter: $10-$200 per share
- Volume requirement: >2x average daily volume
- Float filter: >10M shares outstanding
- News catalyst detection

### 🔧 Technical Indicators
- 20-period Exponential Moving Average (EMA)
- Relative Strength Index (RSI) - 14 period
- Volume Moving Average - 20 period
- Average True Range (ATR) for trailing stops

### 🛡️ Risk Controls
- Maximum 3 concurrent positions
- Daily loss limit: 3% of account
- PDT rule compliance ($25,000 minimum)
- No trading on FOMC days
- Circuit breaker protection

## 🚀 Quick Start

### Installation
```bash
git clone https://github.com/HectorTa1989/MomentumTsunami.git
cd MomentumTsunami
pip install -r requirements.txt
```

### Configuration
```bash
cp config/settings.yaml.example config/settings.yaml
# Edit settings.yaml with your API keys and preferences
```

### Run the Bot
```bash
python src/main.py --mode paper  # Paper trading mode
python src/main.py --mode live   # Live trading mode
```

## 📈 Performance Tracking

The system tracks comprehensive metrics including:
- Win/loss ratio and success rate
- Average hold time analysis
- Sector performance breakdown
- Gap size vs success rate correlation
- Risk-adjusted returns (Sharpe ratio)
- Maximum drawdown analysis

## 🔧 Configuration

Key configuration parameters:
- **Account size**: Minimum $25,000 for PDT compliance
- **Risk per trade**: Default 1% of account
- **Max positions**: Default 3 concurrent trades
- **Gap range**: 3-8% (configurable)
- **Time filters**: Market hours and restricted days

## 📚 Documentation

- [Installation Guide](docs/installation.md)
- [Configuration Reference](docs/configuration.md)
- [API Documentation](docs/api_reference.md)
- [Strategy Backtesting](docs/backtesting.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

This software is for educational and research purposes only. Trading involves substantial risk and is not suitable for all investors. Past performance does not guarantee future results. Always consult with a qualified financial advisor before making investment decisions.

## 👨‍💻 Author

**Hector Ta** - [@HectorTa1989](https://github.com/HectorTa1989)

---

*Built with ❤️ for algorithmic trading enthusiasts*
