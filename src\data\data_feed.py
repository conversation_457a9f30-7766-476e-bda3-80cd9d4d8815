#!/usr/bin/env python3
"""
Data feed manager for MomentumTsunami trading bot.
Manages multiple data providers with failover capabilities.
"""

import asyncio
from typing import Dict, List, Optional, Any
import pandas as pd
from datetime import datetime, timedelta

from .market_data import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ygonProvider, YFinanceProvider
from utils.logger import setup_logger
from utils.helpers import retry_async


class DataFeed:
    """
    Main data feed manager that handles multiple data providers
    with automatic failover and caching.
    """
    
    def __init__(self, config):
        self.config = config
        self.logger = setup_logger("DataFeed", config.logging)
        
        # Initialize providers
        self.providers = {}
        self.primary_provider = None
        self.backup_providers = []
        
        # Cache for market data
        self.quote_cache = {}
        self.historical_cache = {}
        self.cache_timestamps = {}
        
        # Provider status tracking
        self.provider_status = {}
        
        self._initialize_providers()
    
    def _initialize_providers(self):
        """Initialize all configured data providers."""
        # Alpha Vantage
        if self.config.api.alpha_vantage_key:
            self.providers['alpha_vantage'] = AlphaVantageProvider(self.config)
            self.provider_status['alpha_vantage'] = True
        
        # Polygon.io
        if self.config.api.polygon_key:
            self.providers['polygon'] = PolygonProvider(self.config)
            self.provider_status['polygon'] = True
        
        # Yahoo Finance (always available as backup)
        self.providers['yfinance'] = YFinanceProvider(self.config)
        self.provider_status['yfinance'] = True
        
        # Set primary and backup providers
        primary = self.config.data.primary_provider
        if primary in self.providers:
            self.primary_provider = self.providers[primary]
        else:
            # Fallback to first available provider
            self.primary_provider = next(iter(self.providers.values()))
        
        # Set backup providers
        for provider_name in self.config.data.backup_providers:
            if provider_name in self.providers and provider_name != self.config.data.primary_provider:
                self.backup_providers.append(self.providers[provider_name])
        
        self.logger.info(f"Initialized {len(self.providers)} data providers")
        self.logger.info(f"Primary provider: {self.config.data.primary_provider}")
        self.logger.info(f"Backup providers: {self.config.data.backup_providers}")
    
    async def initialize(self):
        """Initialize all data providers."""
        for name, provider in self.providers.items():
            try:
                await provider.initialize()
                self.logger.info(f"Initialized {name} provider")
            except Exception as e:
                self.logger.error(f"Failed to initialize {name} provider: {e}")
                self.provider_status[name] = False
    
    async def cleanup(self):
        """Cleanup all data providers."""
        for name, provider in self.providers.items():
            try:
                await provider.cleanup()
                self.logger.info(f"Cleaned up {name} provider")
            except Exception as e:
                self.logger.error(f"Error cleaning up {name} provider: {e}")
    
    @retry_async(max_retries=2, delay=0.5)
    async def get_real_time_data(self, symbol: str) -> Dict[str, Any]:
        """
        Get real-time quote data with caching and failover.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Real-time quote data
        """
        # Check cache first
        if self._is_cache_valid(symbol, 'quote'):
            self.logger.debug(f"Returning cached quote for {symbol}")
            return self.quote_cache[symbol]
        
        # Try primary provider first
        quote_data = await self._get_quote_from_provider(self.primary_provider, symbol)
        
        # If primary fails, try backup providers
        if not quote_data:
            for backup_provider in self.backup_providers:
                quote_data = await self._get_quote_from_provider(backup_provider, symbol)
                if quote_data:
                    break
        
        # Cache the result if successful
        if quote_data:
            self.quote_cache[symbol] = quote_data
            self.cache_timestamps[f"{symbol}_quote"] = datetime.now()
        
        return quote_data or {}
    
    async def _get_quote_from_provider(self, provider, symbol: str) -> Dict[str, Any]:
        """Get quote from a specific provider."""
        try:
            return await provider.get_quote(symbol)
        except Exception as e:
            provider_name = provider.__class__.__name__
            self.logger.warning(f"Failed to get quote from {provider_name}: {e}")
            return {}
    
    async def get_historical_data(self, symbol: str, period: str = "1y") -> pd.DataFrame:
        """
        Get historical data with caching and failover.
        
        Args:
            symbol: Stock symbol
            period: Time period (1y, 6m, 3m, 1m)
            
        Returns:
            Historical data DataFrame
        """
        cache_key = f"{symbol}_{period}"
        
        # Check cache first
        if self._is_cache_valid(cache_key, 'historical'):
            self.logger.debug(f"Returning cached historical data for {symbol}")
            return self.historical_cache[cache_key]
        
        # Try primary provider first
        data = await self._get_historical_from_provider(self.primary_provider, symbol, period)
        
        # If primary fails, try backup providers
        if data.empty:
            for backup_provider in self.backup_providers:
                data = await self._get_historical_from_provider(backup_provider, symbol, period)
                if not data.empty:
                    break
        
        # Cache the result if successful
        if not data.empty:
            self.historical_cache[cache_key] = data
            self.cache_timestamps[f"{cache_key}_historical"] = datetime.now()
        
        return data
    
    async def _get_historical_from_provider(self, provider, symbol: str, period: str) -> pd.DataFrame:
        """Get historical data from a specific provider."""
        try:
            return await provider.get_historical_data(symbol, period)
        except Exception as e:
            provider_name = provider.__class__.__name__
            self.logger.warning(f"Failed to get historical data from {provider_name}: {e}")
            return pd.DataFrame()
    
    async def get_intraday_data(self, symbol: str, interval: str = "1min") -> pd.DataFrame:
        """
        Get intraday data with failover.
        
        Args:
            symbol: Stock symbol
            interval: Time interval (1min, 5min, 15min)
            
        Returns:
            Intraday data DataFrame
        """
        # Try primary provider first
        data = await self._get_intraday_from_provider(self.primary_provider, symbol, interval)
        
        # If primary fails, try backup providers
        if data.empty:
            for backup_provider in self.backup_providers:
                data = await self._get_intraday_from_provider(backup_provider, symbol, interval)
                if not data.empty:
                    break
        
        return data
    
    async def _get_intraday_from_provider(self, provider, symbol: str, interval: str) -> pd.DataFrame:
        """Get intraday data from a specific provider."""
        try:
            return await provider.get_intraday_data(symbol, interval)
        except Exception as e:
            provider_name = provider.__class__.__name__
            self.logger.warning(f"Failed to get intraday data from {provider_name}: {e}")
            return pd.DataFrame()
    
    async def get_multiple_quotes(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        Get quotes for multiple symbols concurrently.
        
        Args:
            symbols: List of stock symbols
            
        Returns:
            Dictionary mapping symbols to quote data
        """
        tasks = [self.get_real_time_data(symbol) for symbol in symbols]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        quotes = {}
        for symbol, result in zip(symbols, results):
            if isinstance(result, Exception):
                self.logger.error(f"Error getting quote for {symbol}: {result}")
                quotes[symbol] = {}
            else:
                quotes[symbol] = result
        
        return quotes
    
    async def search_symbols(self, query: str) -> List[Dict[str, Any]]:
        """
        Search for symbols matching query.
        
        Args:
            query: Search query
            
        Returns:
            List of matching symbols
        """
        # Try primary provider first
        try:
            results = await self.primary_provider.search_symbols(query)
            if results:
                return results
        except Exception as e:
            self.logger.warning(f"Primary provider search failed: {e}")
        
        # Try backup providers
        for backup_provider in self.backup_providers:
            try:
                results = await backup_provider.search_symbols(query)
                if results:
                    return results
            except Exception as e:
                self.logger.warning(f"Backup provider search failed: {e}")
        
        return []
    
    def _is_cache_valid(self, key: str, cache_type: str) -> bool:
        """Check if cached data is still valid."""
        if not self.config.data.cache_enabled:
            return False
        
        cache_key = f"{key}_{cache_type}"
        
        if cache_type == 'quote':
            if key not in self.quote_cache:
                return False
            # Quote cache valid for 1 minute
            cache_duration = timedelta(minutes=1)
        elif cache_type == 'historical':
            if key not in self.historical_cache:
                return False
            # Historical cache valid for configured duration
            cache_duration = timedelta(hours=self.config.data.cache_duration_hours)
        else:
            return False
        
        if cache_key not in self.cache_timestamps:
            return False
        
        cache_time = self.cache_timestamps[cache_key]
        return datetime.now() - cache_time < cache_duration
    
    def clear_cache(self):
        """Clear all cached data."""
        self.quote_cache.clear()
        self.historical_cache.clear()
        self.cache_timestamps.clear()
        self.logger.info("Cleared all cached data")
    
    def get_provider_status(self) -> Dict[str, bool]:
        """Get status of all data providers."""
        return self.provider_status.copy()
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on all providers."""
        health_status = {
            'timestamp': datetime.now().isoformat(),
            'providers': {}
        }
        
        # Test each provider with a simple quote request
        test_symbol = "AAPL"
        
        for name, provider in self.providers.items():
            try:
                start_time = datetime.now()
                quote = await provider.get_quote(test_symbol)
                response_time = (datetime.now() - start_time).total_seconds()
                
                health_status['providers'][name] = {
                    'status': 'healthy' if quote else 'degraded',
                    'response_time_seconds': response_time,
                    'last_check': datetime.now().isoformat()
                }
                
                self.provider_status[name] = bool(quote)
                
            except Exception as e:
                health_status['providers'][name] = {
                    'status': 'unhealthy',
                    'error': str(e),
                    'last_check': datetime.now().isoformat()
                }
                
                self.provider_status[name] = False
        
        return health_status
