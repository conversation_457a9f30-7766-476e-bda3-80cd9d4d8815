#!/usr/bin/env python3
"""
Performance reporting script for MomentumTsunami trading bot.
"""

import argparse
import sys
from pathlib import Path
from datetime import datetime, timedelta

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from config import load_config
from analytics.performance_tracker import PerformanceTracker


def main():
    parser = argparse.ArgumentParser(description="MomentumTsunami Performance Report")
    parser.add_argument("--config", type=str, default="config/settings.yaml",
                       help="Configuration file path")
    parser.add_argument("--days", type=int, default=30,
                       help="Number of days to analyze")
    parser.add_argument("--export-csv", type=str,
                       help="Export trades to CSV file")
    parser.add_argument("--symbol", type=str,
                       help="Analyze specific symbol only")
    parser.add_argument("--strategy", type=str,
                       help="Analyze specific strategy only")
    
    args = parser.parse_args()
    
    # Load configuration
    config = load_config(args.config)
    
    # Create performance tracker
    tracker = PerformanceTracker(config)
    
    print("MomentumTsunami Performance Report")
    print("=" * 50)
    print(f"Report Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Analysis Period: Last {args.days} days")
    print()
    
    # Load trades from database (if available)
    # For demo purposes, we'll show how the report would look
    
    if args.symbol:
        print(f"Symbol Analysis: {args.symbol}")
        print("-" * 30)
        
        # Get symbol-specific performance
        symbol_performance = tracker.get_symbol_performance(args.symbol)
        display_performance_metrics(symbol_performance, f"{args.symbol} Performance")
        
    elif args.strategy:
        print(f"Strategy Analysis: {args.strategy}")
        print("-" * 30)
        
        # Get strategy-specific performance
        strategy_performance = tracker.get_strategy_performance(args.strategy)
        display_performance_metrics(strategy_performance, f"{args.strategy} Strategy")
        
    else:
        # Overall performance
        overall_performance = tracker.calculate_performance_metrics()
        display_performance_metrics(overall_performance, "Overall Performance")
        
        # Gap analysis
        print("\nGap Size Analysis:")
        print("-" * 30)
        gap_analysis = tracker.get_gap_analysis()
        
        if 'message' in gap_analysis:
            print(gap_analysis['message'])
        else:
            for gap_range, stats in gap_analysis.items():
                print(f"{gap_range:>8}: {stats['trade_count']:>3} trades, "
                      f"{stats['win_rate']:>5.1f}% win rate, "
                      f"${stats['avg_pnl']:>7.2f} avg P&L")
    
    # Export to CSV if requested
    if args.export_csv:
        start_date = (datetime.now() - timedelta(days=args.days)).strftime('%Y-%m-%d')
        end_date = datetime.now().strftime('%Y-%m-%d')
        
        success = tracker.export_trades_to_csv(args.export_csv, start_date, end_date)
        if success:
            print(f"\nTrades exported to {args.export_csv}")
        else:
            print(f"\nFailed to export trades to {args.export_csv}")
    
    # Generate full report
    print("\n" + "=" * 50)
    print("DETAILED PERFORMANCE REPORT")
    print("=" * 50)
    
    report = tracker.generate_performance_report()
    print(report)


def display_performance_metrics(metrics, title):
    """Display performance metrics in a formatted table."""
    print(f"\n{title}:")
    print("-" * len(title))
    
    if not metrics or metrics.get('total_trades', 0) == 0:
        print("No trading data available for the specified period.")
        return
    
    # Basic metrics
    print(f"Total Trades:        {metrics['total_trades']:>8}")
    print(f"Winning Trades:      {metrics['winning_trades']:>8}")
    print(f"Losing Trades:       {metrics['losing_trades']:>8}")
    print(f"Win Rate:            {metrics['win_rate']:>7.1f}%")
    print()
    
    # P&L metrics
    print(f"Total P&L:           ${metrics['total_pnl']:>8,.2f}")
    print(f"Average P&L/Trade:   ${metrics['avg_pnl_per_trade']:>8,.2f}")
    print(f"Average Win:         ${metrics['avg_win']:>8,.2f}")
    print(f"Average Loss:        ${metrics['avg_loss']:>8,.2f}")
    print(f"Largest Win:         ${metrics['largest_win']:>8,.2f}")
    print(f"Largest Loss:        ${metrics['largest_loss']:>8,.2f}")
    print()
    
    # Risk metrics
    print(f"Profit Factor:       {metrics['profit_factor']:>8.2f}")
    print(f"Sharpe Ratio:        {metrics['sharpe_ratio']:>8.2f}")
    print(f"Max Drawdown:        ${metrics['max_drawdown']:>8,.2f}")
    print()
    
    # Volume metrics
    print(f"Total Volume:        {metrics['total_volume']:>8,} shares")
    print(f"Avg Hold Time:       {metrics['avg_hold_time_minutes']:>7.1f} minutes")
    
    # Performance rating
    rating = calculate_performance_rating(metrics)
    print(f"\nPerformance Rating:  {rating}")


def calculate_performance_rating(metrics):
    """Calculate a simple performance rating based on key metrics."""
    if metrics['total_trades'] < 10:
        return "Insufficient Data"
    
    score = 0
    
    # Win rate scoring (0-30 points)
    win_rate = metrics['win_rate']
    if win_rate >= 70:
        score += 30
    elif win_rate >= 60:
        score += 25
    elif win_rate >= 50:
        score += 20
    elif win_rate >= 40:
        score += 10
    
    # Profit factor scoring (0-30 points)
    profit_factor = metrics['profit_factor']
    if profit_factor >= 2.0:
        score += 30
    elif profit_factor >= 1.5:
        score += 25
    elif profit_factor >= 1.2:
        score += 20
    elif profit_factor >= 1.0:
        score += 10
    
    # Sharpe ratio scoring (0-25 points)
    sharpe_ratio = metrics['sharpe_ratio']
    if sharpe_ratio >= 2.0:
        score += 25
    elif sharpe_ratio >= 1.5:
        score += 20
    elif sharpe_ratio >= 1.0:
        score += 15
    elif sharpe_ratio >= 0.5:
        score += 10
    
    # Total P&L scoring (0-15 points)
    if metrics['total_pnl'] > 0:
        score += 15
    elif metrics['total_pnl'] > -500:
        score += 5
    
    # Convert to rating
    if score >= 85:
        return "Excellent ⭐⭐⭐⭐⭐"
    elif score >= 70:
        return "Very Good ⭐⭐⭐⭐"
    elif score >= 55:
        return "Good ⭐⭐⭐"
    elif score >= 40:
        return "Fair ⭐⭐"
    elif score >= 25:
        return "Poor ⭐"
    else:
        return "Very Poor"


if __name__ == "__main__":
    main()
