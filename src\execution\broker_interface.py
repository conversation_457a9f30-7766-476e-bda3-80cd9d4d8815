#!/usr/bin/env python3
"""
Broker interface implementations for MomentumTsunami trading bot.
"""

import asyncio
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from datetime import datetime
import uuid
from dataclasses import dataclass

from utils.logger import setup_logger


@dataclass
class Order:
    """Order data structure."""
    order_id: str
    symbol: str
    side: str  # 'buy' or 'sell'
    quantity: int
    order_type: str  # 'market', 'limit', 'stop', 'stop_limit'
    price: Optional[float] = None
    stop_price: Optional[float] = None
    time_in_force: str = 'day'  # 'day', 'gtc', 'ioc', 'fok'
    status: str = 'pending'  # 'pending', 'filled', 'cancelled', 'rejected'
    filled_quantity: int = 0
    fill_price: Optional[float] = None
    created_at: datetime = None
    filled_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


@dataclass
class Position:
    """Position data structure."""
    symbol: str
    quantity: int
    avg_price: float
    market_value: float
    unrealized_pnl: float
    side: str  # 'long' or 'short'


class BrokerInterface(ABC):
    """Abstract base class for broker interfaces."""
    
    def __init__(self, config):
        self.config = config
        self.logger = setup_logger(f"Broker.{self.__class__.__name__}", config.logging)
    
    @abstractmethod
    async def initialize(self):
        """Initialize broker connection."""
        pass
    
    @abstractmethod
    async def cleanup(self):
        """Cleanup broker connection."""
        pass
    
    @abstractmethod
    async def place_order(self, symbol: str, side: str, quantity: int, 
                         order_type: str = 'market', price: Optional[float] = None,
                         stop_price: Optional[float] = None) -> Optional[Order]:
        """Place an order."""
        pass
    
    @abstractmethod
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel an order."""
        pass
    
    @abstractmethod
    async def get_order_status(self, order_id: str) -> Optional[Order]:
        """Get order status."""
        pass
    
    @abstractmethod
    async def get_positions(self) -> Dict[str, Position]:
        """Get current positions."""
        pass
    
    @abstractmethod
    async def get_account_info(self) -> Dict[str, Any]:
        """Get account information."""
        pass


class PaperTradingBroker(BrokerInterface):
    """
    Paper trading broker implementation for testing and simulation.
    """
    
    def __init__(self, config):
        super().__init__(config)
        
        # Paper trading state
        self.orders: Dict[str, Order] = {}
        self.positions: Dict[str, Position] = {}
        self.account_balance = config.account.account_size
        self.buying_power = config.account.account_size
        self.order_counter = 0
        
        # Simulation parameters
        self.fill_delay_seconds = 1  # Simulate order fill delay
        self.slippage_percent = 0.05  # 0.05% slippage simulation
        
        self.logger.info(f"Paper trading broker initialized with ${self.account_balance:,.2f}")
    
    async def initialize(self):
        """Initialize paper trading broker."""
        self.logger.info("Paper trading broker ready")
    
    async def cleanup(self):
        """Cleanup paper trading broker."""
        self.logger.info("Paper trading broker cleanup complete")
    
    async def place_order(self, symbol: str, side: str, quantity: int,
                         order_type: str = 'market', price: Optional[float] = None,
                         stop_price: Optional[float] = None) -> Optional[Order]:
        """
        Place a paper trading order.
        
        Args:
            symbol: Stock symbol
            side: 'buy' or 'sell'
            quantity: Number of shares
            order_type: Order type
            price: Limit price (for limit orders)
            stop_price: Stop price (for stop orders)
            
        Returns:
            Order object or None if failed
        """
        try:
            self.order_counter += 1
            order_id = f"PAPER_{self.order_counter}_{uuid.uuid4().hex[:8]}"
            
            order = Order(
                order_id=order_id,
                symbol=symbol,
                side=side,
                quantity=quantity,
                order_type=order_type,
                price=price,
                stop_price=stop_price
            )
            
            self.orders[order_id] = order
            
            self.logger.info(f"Paper order placed: {order_id} - {side.upper()} {quantity} {symbol} @ {order_type}")
            
            # Simulate order processing
            asyncio.create_task(self._process_order(order))
            
            return order
            
        except Exception as e:
            self.logger.error(f"Error placing paper order: {e}")
            return None
    
    async def _process_order(self, order: Order):
        """Process paper trading order (simulate fill)."""
        try:
            # Simulate processing delay
            await asyncio.sleep(self.fill_delay_seconds)
            
            # For market orders, simulate immediate fill
            if order.order_type == 'market':
                # Get simulated market price (would normally come from data feed)
                market_price = await self._get_simulated_market_price(order.symbol)
                
                if market_price is None:
                    order.status = 'rejected'
                    self.logger.warning(f"Order rejected - no market price for {order.symbol}")
                    return
                
                # Apply slippage
                if order.side == 'buy':
                    fill_price = market_price * (1 + self.slippage_percent / 100)
                else:
                    fill_price = market_price * (1 - self.slippage_percent / 100)
                
                # Check buying power for buy orders
                if order.side == 'buy':
                    required_capital = fill_price * order.quantity
                    if required_capital > self.buying_power:
                        order.status = 'rejected'
                        self.logger.warning(f"Order rejected - insufficient buying power")
                        return
                
                # Fill the order
                order.status = 'filled'
                order.filled_quantity = order.quantity
                order.fill_price = fill_price
                order.filled_at = datetime.now()
                
                # Update positions and account
                await self._update_position(order)
                
                self.logger.info(f"Paper order filled: {order.order_id} @ ${fill_price:.2f}")
            
            else:
                # For other order types, implement more complex logic
                self.logger.warning(f"Order type {order.order_type} not fully implemented in paper trading")
                order.status = 'rejected'
                
        except Exception as e:
            self.logger.error(f"Error processing paper order {order.order_id}: {e}")
            order.status = 'rejected'
    
    async def _get_simulated_market_price(self, symbol: str) -> Optional[float]:
        """Get simulated market price for paper trading."""
        # In a real implementation, this would get the current market price
        # For simulation, we'll use some dummy prices
        
        dummy_prices = {
            'AAPL': 150.00,
            'MSFT': 300.00,
            'GOOGL': 2500.00,
            'TSLA': 200.00,
            'AMZN': 3000.00,
            'META': 250.00,
            'NVDA': 400.00,
            'NFLX': 400.00
        }
        
        base_price = dummy_prices.get(symbol, 100.00)
        
        # Add some random variation (±2%)
        import random
        variation = random.uniform(-0.02, 0.02)
        return base_price * (1 + variation)
    
    async def _update_position(self, order: Order):
        """Update position after order fill."""
        symbol = order.symbol
        
        if symbol not in self.positions:
            # New position
            if order.side == 'buy':
                self.positions[symbol] = Position(
                    symbol=symbol,
                    quantity=order.filled_quantity,
                    avg_price=order.fill_price,
                    market_value=order.fill_price * order.filled_quantity,
                    unrealized_pnl=0.0,
                    side='long'
                )
                
                # Update buying power
                self.buying_power -= order.fill_price * order.filled_quantity
                
        else:
            # Existing position
            position = self.positions[symbol]
            
            if order.side == 'buy' and position.side == 'long':
                # Add to long position
                total_cost = (position.avg_price * position.quantity) + (order.fill_price * order.filled_quantity)
                total_quantity = position.quantity + order.filled_quantity
                
                position.quantity = total_quantity
                position.avg_price = total_cost / total_quantity
                position.market_value = position.avg_price * position.quantity
                
                self.buying_power -= order.fill_price * order.filled_quantity
                
            elif order.side == 'sell' and position.side == 'long':
                # Reduce or close long position
                if order.filled_quantity >= position.quantity:
                    # Close position completely
                    realized_pnl = (order.fill_price - position.avg_price) * position.quantity
                    self.account_balance += realized_pnl
                    self.buying_power += order.fill_price * position.quantity
                    
                    del self.positions[symbol]
                    
                    self.logger.info(f"Position closed: {symbol} - Realized P&L: ${realized_pnl:.2f}")
                    
                else:
                    # Partial close
                    realized_pnl = (order.fill_price - position.avg_price) * order.filled_quantity
                    self.account_balance += realized_pnl
                    self.buying_power += order.fill_price * order.filled_quantity
                    
                    position.quantity -= order.filled_quantity
                    position.market_value = position.avg_price * position.quantity
                    
                    self.logger.info(f"Position reduced: {symbol} - Realized P&L: ${realized_pnl:.2f}")
    
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel a paper trading order."""
        if order_id not in self.orders:
            return False
        
        order = self.orders[order_id]
        
        if order.status == 'pending':
            order.status = 'cancelled'
            self.logger.info(f"Paper order cancelled: {order_id}")
            return True
        
        return False
    
    async def get_order_status(self, order_id: str) -> Optional[Order]:
        """Get paper trading order status."""
        return self.orders.get(order_id)
    
    async def get_positions(self) -> Dict[str, Position]:
        """Get current paper trading positions."""
        return self.positions.copy()
    
    async def get_account_info(self) -> Dict[str, Any]:
        """Get paper trading account information."""
        total_position_value = sum(pos.market_value for pos in self.positions.values())
        total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        
        return {
            'account_balance': self.account_balance,
            'buying_power': self.buying_power,
            'total_position_value': total_position_value,
            'total_unrealized_pnl': total_unrealized_pnl,
            'total_equity': self.account_balance + total_position_value + total_unrealized_pnl,
            'position_count': len(self.positions),
            'day_trade_buying_power': self.buying_power * 4,  # Simulate PDT buying power
            'pattern_day_trader': self.account_balance >= 25000
        }
    
    def get_order_history(self) -> List[Order]:
        """Get order history."""
        return list(self.orders.values())
    
    def get_filled_orders(self) -> List[Order]:
        """Get filled orders."""
        return [order for order in self.orders.values() if order.status == 'filled']
    
    def reset_account(self):
        """Reset paper trading account."""
        self.orders.clear()
        self.positions.clear()
        self.account_balance = self.config.account.account_size
        self.buying_power = self.config.account.account_size
        self.order_counter = 0
        
        self.logger.info("Paper trading account reset")


# Placeholder for real broker implementations
class AlpacaBroker(BrokerInterface):
    """Alpaca broker implementation (placeholder)."""
    
    async def initialize(self):
        self.logger.info("Alpaca broker would be initialized here")
    
    async def cleanup(self):
        pass
    
    async def place_order(self, symbol: str, side: str, quantity: int,
                         order_type: str = 'market', price: Optional[float] = None,
                         stop_price: Optional[float] = None) -> Optional[Order]:
        self.logger.warning("Alpaca broker not implemented - use paper trading")
        return None
    
    async def cancel_order(self, order_id: str) -> bool:
        return False
    
    async def get_order_status(self, order_id: str) -> Optional[Order]:
        return None
    
    async def get_positions(self) -> Dict[str, Position]:
        return {}
    
    async def get_account_info(self) -> Dict[str, Any]:
        return {}


class InteractiveBrokersBroker(BrokerInterface):
    """Interactive Brokers implementation (placeholder)."""
    
    async def initialize(self):
        self.logger.info("Interactive Brokers would be initialized here")
    
    async def cleanup(self):
        pass
    
    async def place_order(self, symbol: str, side: str, quantity: int,
                         order_type: str = 'market', price: Optional[float] = None,
                         stop_price: Optional[float] = None) -> Optional[Order]:
        self.logger.warning("Interactive Brokers not implemented - use paper trading")
        return None
    
    async def cancel_order(self, order_id: str) -> bool:
        return False
    
    async def get_order_status(self, order_id: str) -> Optional[Order]:
        return None
    
    async def get_positions(self) -> Dict[str, Position]:
        return {}
    
    async def get_account_info(self) -> Dict[str, Any]:
        return {}
