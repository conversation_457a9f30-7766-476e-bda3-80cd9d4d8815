# MomentumTsunami Configuration Guide

## Configuration Overview

MomentumTsunami uses a hierarchical configuration system with multiple sources:

1. **Default Configuration**: Built-in defaults in `config/config.py`
2. **YAML Configuration**: User settings in `config/settings.yaml`
3. **Environment Variables**: Override sensitive settings
4. **Command Line Arguments**: Runtime parameters

## Configuration File Structure

### Main Configuration Sections

```yaml
# config/settings.yaml

api:
  # Data provider API keys
  alpha_vantage_key: ""
  polygon_key: ""
  iex_token: ""
  broker_api_key: ""
  broker_secret: ""
  broker_base_url: ""

trading:
  # Gap scanning parameters
  min_gap_percent: 3.0
  max_gap_percent: 8.0
  min_price: 10.0
  max_price: 200.0
  min_volume_multiplier: 2.0
  min_float_shares: ********
  
  # Entry conditions
  ema_period: 20
  rsi_period: 14
  rsi_min: 40.0
  rsi_max: 60.0
  volume_surge_multiplier: 1.5
  market_open_delay_minutes: 15
  
  # Risk management
  risk_per_trade_percent: 1.0
  max_positions: 3
  daily_loss_limit_percent: 3.0
  stop_loss_percent: 2.0
  profit_target_ratio: 1.5
  trailing_stop_atr_multiplier: 0.5
  
  # Time management
  market_close_time: "15:30"
  no_trade_days:
    - "FOMC"

account:
  account_size: 25000.0
  broker: "paper"
  paper_trading: true
  min_account_size: 25000.0

data:
  primary_provider: "alpha_vantage"
  backup_providers:
    - "polygon"
    - "iex"
  update_interval_seconds: 60
  historical_days: 252
  cache_enabled: true
  cache_duration_hours: 24

logging:
  level: "INFO"
  log_dir: "data/logs"
  max_file_size: "10MB"
  backup_count: 5
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
```

## Detailed Configuration Options

### API Configuration

#### Data Provider Settings
```yaml
api:
  alpha_vantage_key: "YOUR_API_KEY"     # Alpha Vantage API key
  polygon_key: "YOUR_API_KEY"           # Polygon.io API key
  iex_token: "YOUR_TOKEN"               # IEX Cloud token
```

**Environment Variable Overrides:**
- `ALPHA_VANTAGE_API_KEY`
- `POLYGON_API_KEY`
- `IEX_TOKEN`

#### Broker Settings
```yaml
api:
  broker_api_key: "YOUR_BROKER_KEY"     # Broker API key
  broker_secret: "YOUR_BROKER_SECRET"   # Broker API secret
  broker_base_url: "https://paper-api.alpaca.markets"  # Broker API URL
```

**Environment Variable Overrides:**
- `BROKER_API_KEY`
- `BROKER_SECRET`

### Trading Configuration

#### Gap Scanning Parameters
```yaml
trading:
  min_gap_percent: 3.0          # Minimum gap percentage to consider
  max_gap_percent: 8.0          # Maximum gap percentage to consider
  min_price: 10.0               # Minimum stock price ($)
  max_price: 200.0              # Maximum stock price ($)
  min_volume_multiplier: 2.0    # Minimum volume vs average (2x = 200%)
  min_float_shares: ********    # Minimum float shares (10M)
```

**Recommended Ranges:**
- **Gap Range**: 3-8% provides good balance of opportunity and risk
- **Price Range**: $10-$200 avoids penny stocks and very expensive stocks
- **Volume**: 2x average ensures sufficient liquidity
- **Float**: 10M+ shares reduces manipulation risk

#### Entry Conditions
```yaml
trading:
  ema_period: 20                    # EMA period for pullback entry
  rsi_period: 14                    # RSI calculation period
  rsi_min: 40.0                     # Minimum RSI for entry
  rsi_max: 60.0                     # Maximum RSI for entry
  volume_surge_multiplier: 1.5      # Volume surge requirement (1.5x)
  market_open_delay_minutes: 15     # Wait time after market open
```

**Strategy Notes:**
- **20 EMA**: Standard momentum indicator
- **RSI 40-60**: Avoids oversold/overbought conditions
- **Volume Surge**: Confirms momentum
- **15-minute delay**: Allows initial volatility to settle

#### Risk Management
```yaml
trading:
  risk_per_trade_percent: 1.0       # Risk per trade (% of account)
  max_positions: 3                  # Maximum concurrent positions
  daily_loss_limit_percent: 3.0     # Daily loss limit (% of account)
  stop_loss_percent: 2.0            # Default stop loss (% below entry)
  profit_target_ratio: 1.5          # Risk/reward ratio (1.5:1)
  trailing_stop_atr_multiplier: 0.5 # ATR multiplier for trailing stops
```

**Risk Guidelines:**
- **1% per trade**: Conservative risk management
- **3 positions max**: Prevents over-concentration
- **3% daily limit**: Circuit breaker protection
- **2% stop loss**: Limits individual trade losses
- **1.5:1 R/R**: Positive expectancy target

#### Time Management
```yaml
trading:
  market_close_time: "15:30"        # Time to close all positions (EST)
  no_trade_days:                    # Days to avoid trading
    - "FOMC"                        # Federal Reserve meeting days
    - "OPEX"                        # Options expiration (optional)
    - "EARNINGS"                    # Major earnings days (optional)
```

### Account Configuration

```yaml
account:
  account_size: 25000.0             # Total account size
  broker: "paper"                   # Broker type: paper, alpaca, ib
  paper_trading: true               # Enable paper trading mode
  min_account_size: 25000.0         # Minimum for PDT compliance
```

**Account Types:**
- **Paper Trading**: Risk-free simulation mode
- **Live Trading**: Real money trading (requires $25,000+)

### Data Configuration

```yaml
data:
  primary_provider: "alpha_vantage"  # Primary data source
  backup_providers:                  # Backup data sources (failover)
    - "polygon"
    - "iex"
  update_interval_seconds: 60        # Data refresh interval
  historical_days: 252               # Historical data period (1 year)
  cache_enabled: true                # Enable data caching
  cache_duration_hours: 24           # Cache expiration time
```

**Data Provider Priority:**
1. **Alpha Vantage**: Free tier available, good for testing
2. **Polygon.io**: Professional-grade data, real-time
3. **IEX Cloud**: Good free tier, reliable
4. **Yahoo Finance**: Backup option, free but limited

### Logging Configuration

```yaml
logging:
  level: "INFO"                     # Log level: DEBUG, INFO, WARNING, ERROR
  log_dir: "data/logs"              # Log file directory
  max_file_size: "10MB"             # Maximum log file size
  backup_count: 5                   # Number of backup log files
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
```

**Log Levels:**
- **DEBUG**: Detailed debugging information
- **INFO**: General operational information
- **WARNING**: Warning messages
- **ERROR**: Error messages only

## Environment Variables

### Security Best Practices

Never store sensitive information in configuration files. Use environment variables:

```bash
# .env file (not committed to version control)
ALPHA_VANTAGE_API_KEY=your_actual_key_here
POLYGON_API_KEY=your_actual_key_here
IEX_TOKEN=your_actual_token_here
BROKER_API_KEY=your_broker_key_here
BROKER_SECRET=your_broker_secret_here

# Account settings
ACCOUNT_SIZE=25000
PAPER_TRADING=true
DEBUG=false
```

### Loading Environment Variables

```python
# Automatic loading in config.py
import os
from dotenv import load_dotenv

load_dotenv()  # Load .env file

# Access in code
api_key = os.getenv('ALPHA_VANTAGE_API_KEY')
```

## Command Line Arguments

Override configuration at runtime:

```bash
# Basic usage
python src/main.py --mode paper --debug

# All available options
python src/main.py \
  --mode paper \
  --config custom_config.yaml \
  --debug \
  --account-size 50000 \
  --risk-per-trade 0.5
```

**Available Arguments:**
- `--mode`: Trading mode (paper, live, backtest)
- `--config`: Custom configuration file path
- `--debug`: Enable debug logging
- `--account-size`: Override account size
- `--risk-per-trade`: Override risk per trade percentage

## Configuration Validation

The system validates configuration on startup:

```python
# Validation checks
- Account size >= $25,000 for live trading
- Gap percentages: min < max
- Price range: min < max
- Risk percentages: 0 < risk <= 10%
- API keys present for selected providers
```

## Configuration Profiles

Create different profiles for different scenarios:

```bash
# Development profile
config/dev_settings.yaml

# Production profile
config/prod_settings.yaml

# Backtesting profile
config/backtest_settings.yaml
```

Load specific profile:
```bash
python src/main.py --config config/prod_settings.yaml
```

## Advanced Configuration

### Custom Stock Universe

```python
# In gap_scanner.py, modify stock_universe
self.stock_universe = [
    'AAPL', 'MSFT', 'GOOGL',  # Your custom list
    # Add more symbols as needed
]
```

### Custom Indicators

```yaml
# Add custom indicator parameters
trading:
  custom_indicators:
    bollinger_bands:
      period: 20
      std_dev: 2.0
    macd:
      fast_period: 12
      slow_period: 26
      signal_period: 9
```

### Database Configuration

```yaml
# For production database
database:
  type: "postgresql"
  host: "localhost"
  port: 5432
  name: "momentum_tsunami"
  user: "trader"
  password: "${DB_PASSWORD}"  # Environment variable
```

## Troubleshooting Configuration

### Common Issues

1. **API Key Errors**
   - Verify keys are correct
   - Check environment variable names
   - Ensure keys have proper permissions

2. **Validation Errors**
   - Check parameter ranges
   - Verify account size for live trading
   - Ensure required fields are present

3. **Data Provider Issues**
   - Test API connectivity
   - Check rate limits
   - Verify subscription status

### Configuration Testing

```python
# Test configuration loading
from config import load_config

config = load_config('config/settings.yaml')
print(f"Account size: ${config.account.account_size:,.2f}")
print(f"Risk per trade: {config.trading.risk_per_trade_percent}%")
```

## Best Practices

1. **Start Conservative**: Use small risk percentages initially
2. **Test Thoroughly**: Validate all settings in paper trading
3. **Monitor Performance**: Adjust parameters based on results
4. **Keep Backups**: Save working configurations
5. **Document Changes**: Track configuration modifications
6. **Security First**: Never commit API keys to version control

## Configuration Examples

### Conservative Day Trading
```yaml
trading:
  risk_per_trade_percent: 0.5
  max_positions: 2
  daily_loss_limit_percent: 2.0
  min_gap_percent: 4.0
  max_gap_percent: 6.0
```

### Aggressive Day Trading
```yaml
trading:
  risk_per_trade_percent: 2.0
  max_positions: 5
  daily_loss_limit_percent: 5.0
  min_gap_percent: 3.0
  max_gap_percent: 10.0
```

### Backtesting Configuration
```yaml
trading:
  # Wider parameters for backtesting
  min_gap_percent: 2.0
  max_gap_percent: 12.0
  risk_per_trade_percent: 1.0

data:
  historical_days: 1260  # 5 years of data
  cache_enabled: true
```
