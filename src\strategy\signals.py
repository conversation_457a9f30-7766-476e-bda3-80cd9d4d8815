#!/usr/bin/env python3
"""
Signal generation for MomentumTsunami trading bot.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

from .indicators import TechnicalIndicators
from utils.logger import setup_logger
from utils.helpers import calculate_profit_target


class SignalGenerator:
    """
    Generates trading signals based on technical analysis and momentum criteria.
    """
    
    def __init__(self, config):
        self.config = config
        self.logger = setup_logger("SignalGenerator", config.logging)
        self.indicators = TechnicalIndicators(config)
        
        # Signal configuration
        self.ema_period = config.trading.ema_period
        self.rsi_period = config.trading.rsi_period
        self.rsi_min = config.trading.rsi_min
        self.rsi_max = config.trading.rsi_max
        self.volume_multiplier = config.trading.volume_surge_multiplier
        self.profit_target_ratio = config.trading.profit_target_ratio
        self.stop_loss_percent = config.trading.stop_loss_percent
    
    async def generate_entry_signal(self, stock_data: Dict[str, Any], market_data: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """
        Generate entry signal for gap-up momentum strategy.
        
        Args:
            stock_data: Gap stock data from scanner
            market_data: Intraday market data (5-minute bars)
            
        Returns:
            Entry signal dictionary or None
        """
        if market_data.empty or len(market_data) < self.ema_period:
            self.logger.debug(f"Insufficient data for {stock_data['symbol']}")
            return None
        
        symbol = stock_data['symbol']
        current_price = market_data['close'].iloc[-1]
        current_volume = market_data['volume'].iloc[-1]
        
        # Calculate technical indicators
        indicators = self.indicators.calculate_momentum_indicators(market_data)
        
        # Check entry conditions
        entry_conditions = await self._check_entry_conditions(
            stock_data, market_data, indicators, current_price, current_volume
        )
        
        if not entry_conditions['valid']:
            return None
        
        # Calculate position parameters
        gap_fill_level = stock_data['gap_fill_level']
        stop_loss = max(gap_fill_level, current_price * (1 - self.stop_loss_percent / 100))
        profit_target = calculate_profit_target(current_price, stop_loss, self.profit_target_ratio)
        
        # Calculate entry score
        entry_score = self.indicators.calculate_entry_score(market_data, indicators)
        
        entry_signal = {
            'symbol': symbol,
            'signal_type': 'ENTRY_LONG',
            'price': current_price,
            'stop_loss': stop_loss,
            'profit_target': profit_target,
            'gap_fill_level': gap_fill_level,
            'entry_score': entry_score,
            'risk_reward_ratio': (profit_target - current_price) / (current_price - stop_loss),
            'conditions_met': entry_conditions['conditions'],
            'timestamp': datetime.now().isoformat(),
            'strategy': 'gap_momentum_pullback'
        }
        
        self.logger.info(f"Entry signal generated for {symbol}: ${current_price:.2f} (Score: {entry_score:.2f})")
        
        return entry_signal
    
    async def _check_entry_conditions(self, stock_data: Dict[str, Any], market_data: pd.DataFrame, 
                                    indicators: Dict[str, pd.Series], current_price: float, 
                                    current_volume: float) -> Dict[str, Any]:
        """Check all entry conditions for gap momentum strategy."""
        
        conditions = {}
        
        # 1. Pullback to 20 EMA condition
        ema_20 = indicators['ema_20'][-1]
        if pd.isna(ema_20):
            return {'valid': False, 'conditions': conditions}
        
        # Price should be near EMA (within 1% tolerance)
        ema_distance_percent = abs(current_price - ema_20) / current_price * 100
        conditions['near_ema'] = ema_distance_percent <= 1.0
        
        # 2. RSI condition (avoid oversold/overbought)
        rsi = indicators['rsi'][-1]
        conditions['rsi_favorable'] = self.rsi_min <= rsi <= self.rsi_max if not pd.isna(rsi) else True
        
        # 3. Volume surge condition
        avg_volume = indicators['volume_sma'][-1]
        volume_ratio = current_volume / avg_volume if not pd.isna(avg_volume) and avg_volume > 0 else 1
        conditions['volume_surge'] = volume_ratio >= self.volume_multiplier
        
        # 4. Price action condition (bullish momentum)
        conditions['bullish_momentum'] = self.indicators.is_bullish_momentum(market_data, indicators)
        
        # 5. Gap integrity condition (price still above significant support)
        gap_fill_level = stock_data['gap_fill_level']
        conditions['gap_integrity'] = current_price > gap_fill_level * 1.01  # 1% buffer above gap fill
        
        # 6. Time condition (not too late in the day)
        current_time = datetime.now().time()
        market_close_time = datetime.strptime(self.config.trading.market_close_time, "%H:%M").time()
        time_buffer = timedelta(hours=1)
        cutoff_time = (datetime.combine(datetime.today(), market_close_time) - time_buffer).time()
        conditions['time_valid'] = current_time <= cutoff_time
        
        # 7. MACD confirmation
        macd = indicators['macd'][-1]
        macd_signal = indicators['macd_signal'][-1]
        conditions['macd_bullish'] = macd > macd_signal if not (pd.isna(macd) or pd.isna(macd_signal)) else True
        
        # All conditions must be met
        all_conditions_met = all(conditions.values())
        
        return {
            'valid': all_conditions_met,
            'conditions': conditions
        }
    
    async def generate_exit_signal(self, position: Dict[str, Any], market_data: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """
        Generate exit signal for existing position.
        
        Args:
            position: Current position data
            market_data: Current market data
            
        Returns:
            Exit signal dictionary or None
        """
        if market_data.empty:
            return None
        
        symbol = position['symbol']
        current_price = market_data['close'].iloc[-1]
        entry_price = position['entry_price']
        stop_loss = position['stop_loss']
        profit_target = position['profit_target']
        
        # Calculate technical indicators
        indicators = self.indicators.calculate_momentum_indicators(market_data)
        
        # Check exit conditions
        exit_reason = await self._check_exit_conditions(
            position, market_data, indicators, current_price
        )
        
        if not exit_reason:
            return None
        
        # Calculate P&L
        pnl = (current_price - entry_price) * position['quantity']
        pnl_percent = ((current_price - entry_price) / entry_price) * 100
        
        exit_signal = {
            'symbol': symbol,
            'signal_type': 'EXIT_LONG',
            'price': current_price,
            'reason': exit_reason,
            'pnl': pnl,
            'pnl_percent': pnl_percent,
            'hold_time': datetime.now() - position['entry_time'],
            'timestamp': datetime.now().isoformat()
        }
        
        self.logger.info(f"Exit signal generated for {symbol}: ${current_price:.2f} ({exit_reason})")
        
        return exit_signal
    
    async def _check_exit_conditions(self, position: Dict[str, Any], market_data: pd.DataFrame, 
                                   indicators: Dict[str, pd.Series], current_price: float) -> Optional[str]:
        """Check exit conditions and return reason if exit is needed."""
        
        entry_price = position['entry_price']
        stop_loss = position['stop_loss']
        profit_target = position['profit_target']
        
        # 1. Stop loss hit
        if current_price <= stop_loss:
            return "stop_loss"
        
        # 2. Profit target reached
        if current_price >= profit_target:
            return "profit_target"
        
        # 3. Gap fill (price closes below gap level)
        gap_fill_level = position.get('gap_fill_level', entry_price * 0.95)
        if current_price <= gap_fill_level:
            return "gap_fill"
        
        # 4. Time-based exit (end of day)
        current_time = datetime.now().time()
        market_close_time = datetime.strptime(self.config.trading.market_close_time, "%H:%M").time()
        if current_time >= market_close_time:
            return "time_exit"
        
        # 5. Momentum loss (price closes below EMA on 5-min chart)
        ema_20 = indicators['ema_20'][-1]
        if not pd.isna(ema_20) and current_price < ema_20:
            # Confirm with previous bar to avoid false signals
            if len(market_data) >= 2:
                prev_close = market_data['close'].iloc[-2]
                prev_ema = indicators['ema_20'][-2]
                if not pd.isna(prev_ema) and prev_close < prev_ema:
                    return "momentum_loss"
        
        # 6. Bearish momentum detected
        if self.indicators.is_bearish_momentum(market_data, indicators):
            return "bearish_momentum"
        
        # 7. Volume drying up (significant volume decrease)
        current_volume = market_data['volume'].iloc[-1]
        avg_volume = indicators['volume_sma'][-1]
        if not pd.isna(avg_volume) and avg_volume > 0:
            volume_ratio = current_volume / avg_volume
            if volume_ratio < 0.5:  # Volume less than 50% of average
                return "volume_dryup"
        
        # 8. RSI overbought (above 80)
        rsi = indicators['rsi'][-1]
        if not pd.isna(rsi) and rsi > 80:
            return "overbought"
        
        return None
    
    async def update_trailing_stop(self, position: Dict[str, Any], market_data: pd.DataFrame) -> Optional[float]:
        """
        Update trailing stop loss based on ATR.
        
        Args:
            position: Current position data
            market_data: Current market data
            
        Returns:
            New stop loss level or None if no update
        """
        if market_data.empty or len(market_data) < 14:  # Need enough data for ATR
            return None
        
        current_price = market_data['close'].iloc[-1]
        entry_price = position['entry_price']
        current_stop = position['stop_loss']
        
        # Only trail stop if position is profitable
        if current_price <= entry_price:
            return None
        
        # Calculate ATR
        indicators = self.indicators.calculate_momentum_indicators(market_data)
        atr = indicators['atr'][-1]
        
        if pd.isna(atr):
            return None
        
        # Calculate new trailing stop
        new_stop = self.indicators.get_trailing_stop_level(
            entry_price, current_price, atr, self.config.trading.trailing_stop_atr_multiplier
        )
        
        # Only update if new stop is higher than current stop
        if new_stop > current_stop:
            self.logger.info(f"Updating trailing stop for {position['symbol']}: ${current_stop:.2f} -> ${new_stop:.2f}")
            return new_stop
        
        return None
    
    def calculate_signal_strength(self, conditions: Dict[str, bool]) -> float:
        """
        Calculate overall signal strength based on conditions met.
        
        Args:
            conditions: Dictionary of condition results
            
        Returns:
            Signal strength between 0 and 1
        """
        if not conditions:
            return 0.0
        
        # Weight different conditions
        weights = {
            'near_ema': 0.25,
            'rsi_favorable': 0.15,
            'volume_surge': 0.20,
            'bullish_momentum': 0.20,
            'gap_integrity': 0.10,
            'time_valid': 0.05,
            'macd_bullish': 0.05
        }
        
        weighted_score = 0.0
        total_weight = 0.0
        
        for condition, met in conditions.items():
            if condition in weights:
                weight = weights[condition]
                weighted_score += weight if met else 0
                total_weight += weight
        
        return weighted_score / total_weight if total_weight > 0 else 0.0
    
    def get_signal_summary(self, signal: Dict[str, Any]) -> str:
        """
        Generate human-readable signal summary.
        
        Args:
            signal: Signal dictionary
            
        Returns:
            Signal summary string
        """
        if signal['signal_type'] == 'ENTRY_LONG':
            return (f"{signal['symbol']} ENTRY: ${signal['price']:.2f} "
                   f"(Stop: ${signal['stop_loss']:.2f}, Target: ${signal['profit_target']:.2f}, "
                   f"R/R: {signal['risk_reward_ratio']:.1f}, Score: {signal['entry_score']:.2f})")
        
        elif signal['signal_type'] == 'EXIT_LONG':
            return (f"{signal['symbol']} EXIT: ${signal['price']:.2f} "
                   f"({signal['reason']}, P&L: {signal['pnl_percent']:.1f}%)")
        
        return str(signal)
