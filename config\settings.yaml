# MomentumTsunami Trading Bot Configuration
# Copy this file and customize for your environment

api:
  # Data provider API keys (set via environment variables for security)
  alpha_vantage_key: ""  # Set ALPHA_VANTAGE_API_KEY env var
  polygon_key: ""        # Set POLYGON_API_KEY env var
  iex_token: ""          # Set IEX_TOKEN env var
  
  # Broker API credentials (set via environment variables)
  broker_api_key: ""     # Set BROKER_API_KEY env var
  broker_secret: ""      # Set BROKER_SECRET env var
  broker_base_url: "https://paper-api.alpaca.markets"

trading:
  # Gap scanning parameters
  min_gap_percent: 3.0
  max_gap_percent: 8.0
  min_price: 10.0
  max_price: 200.0
  min_volume_multiplier: 2.0
  min_float_shares: ********
  
  # Entry conditions
  ema_period: 20
  rsi_period: 14
  rsi_min: 40.0
  rsi_max: 60.0
  volume_surge_multiplier: 1.5
  market_open_delay_minutes: 15
  
  # Risk management
  risk_per_trade_percent: 1.0
  max_positions: 3
  daily_loss_limit_percent: 3.0
  stop_loss_percent: 2.0
  profit_target_ratio: 1.5
  trailing_stop_atr_multiplier: 0.5
  
  # Time management
  market_close_time: "15:30"
  no_trade_days:
    - "FOMC"

account:
  account_size: 25000.0
  broker: "paper"  # paper, alpaca, ib
  paper_trading: true
  min_account_size: 25000.0

data:
  primary_provider: "alpha_vantage"
  backup_providers:
    - "polygon"
    - "iex"
  update_interval_seconds: 60
  historical_days: 252
  cache_enabled: true
  cache_duration_hours: 24

logging:
  level: "INFO"
  log_dir: "data/logs"
  max_file_size: "10MB"
  backup_count: 5
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
