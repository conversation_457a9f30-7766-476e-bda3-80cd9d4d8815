#!/usr/bin/env python3
"""
Configuration management for MomentumTsunami trading bot.
"""

import os
import yaml
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from pathlib import Path


@dataclass
class APIConfig:
    """API configuration settings."""
    alpha_vantage_key: str = ""
    polygon_key: str = ""
    iex_token: str = ""
    broker_api_key: str = ""
    broker_secret: str = ""
    broker_base_url: str = ""


@dataclass
class TradingConfig:
    """Trading strategy configuration."""
    # Gap scanning parameters
    min_gap_percent: float = 3.0
    max_gap_percent: float = 8.0
    min_price: float = 10.0
    max_price: float = 200.0
    min_volume_multiplier: float = 2.0
    min_float_shares: int = 10_000_000
    
    # Entry conditions
    ema_period: int = 20
    rsi_period: int = 14
    rsi_min: float = 40.0
    rsi_max: float = 60.0
    volume_surge_multiplier: float = 1.5
    market_open_delay_minutes: int = 15
    
    # Risk management
    risk_per_trade_percent: float = 1.0
    max_positions: int = 3
    daily_loss_limit_percent: float = 3.0
    stop_loss_percent: float = 2.0
    profit_target_ratio: float = 1.5
    trailing_stop_atr_multiplier: float = 0.5
    
    # Time management
    market_close_time: str = "15:30"
    no_trade_days: list = field(default_factory=lambda: ["FOMC"])


@dataclass
class AccountConfig:
    """Account and broker configuration."""
    account_size: float = 25000.0
    broker: str = "paper"  # paper, alpaca, ib, etc.
    paper_trading: bool = True
    min_account_size: float = 25000.0  # PDT rule


@dataclass
class DataConfig:
    """Data feed configuration."""
    primary_provider: str = "alpha_vantage"
    backup_providers: list = field(default_factory=lambda: ["polygon", "iex"])
    update_interval_seconds: int = 60
    historical_days: int = 252
    cache_enabled: bool = True
    cache_duration_hours: int = 24


@dataclass
class LoggingConfig:
    """Logging configuration."""
    level: str = "INFO"
    log_dir: str = "data/logs"
    max_file_size: str = "10MB"
    backup_count: int = 5
    format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"


@dataclass
class Config:
    """Main configuration class."""
    api: APIConfig = field(default_factory=APIConfig)
    trading: TradingConfig = field(default_factory=TradingConfig)
    account: AccountConfig = field(default_factory=AccountConfig)
    data: DataConfig = field(default_factory=DataConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    
    # Runtime settings
    mode: str = "paper"  # paper, live, backtest
    debug: bool = False
    dry_run: bool = False


def load_config(config_path: Optional[str] = None) -> Config:
    """
    Load configuration from YAML file and environment variables.
    
    Args:
        config_path: Path to configuration file
        
    Returns:
        Config object with loaded settings
    """
    if config_path is None:
        config_path = os.path.join(os.path.dirname(__file__), "settings.yaml")
    
    config = Config()
    
    # Load from YAML file if it exists
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            yaml_config = yaml.safe_load(f)
            
        # Update config with YAML values
        if yaml_config:
            _update_config_from_dict(config, yaml_config)
    
    # Override with environment variables
    _load_env_variables(config)
    
    # Validate configuration
    _validate_config(config)
    
    return config


def _update_config_from_dict(config: Config, data: Dict[str, Any]) -> None:
    """Update config object from dictionary."""
    for section_name, section_data in data.items():
        if hasattr(config, section_name) and isinstance(section_data, dict):
            section = getattr(config, section_name)
            for key, value in section_data.items():
                if hasattr(section, key):
                    setattr(section, key, value)


def _load_env_variables(config: Config) -> None:
    """Load configuration from environment variables."""
    # API keys
    config.api.alpha_vantage_key = os.getenv("ALPHA_VANTAGE_API_KEY", config.api.alpha_vantage_key)
    config.api.polygon_key = os.getenv("POLYGON_API_KEY", config.api.polygon_key)
    config.api.iex_token = os.getenv("IEX_TOKEN", config.api.iex_token)
    config.api.broker_api_key = os.getenv("BROKER_API_KEY", config.api.broker_api_key)
    config.api.broker_secret = os.getenv("BROKER_SECRET", config.api.broker_secret)
    
    # Trading settings
    if os.getenv("ACCOUNT_SIZE"):
        config.account.account_size = float(os.getenv("ACCOUNT_SIZE"))
    
    if os.getenv("PAPER_TRADING"):
        config.account.paper_trading = os.getenv("PAPER_TRADING").lower() == "true"
    
    if os.getenv("DEBUG"):
        config.debug = os.getenv("DEBUG").lower() == "true"


def _validate_config(config: Config) -> None:
    """Validate configuration settings."""
    # Check minimum account size for PDT rule
    if not config.account.paper_trading and config.account.account_size < config.account.min_account_size:
        raise ValueError(f"Account size must be at least ${config.account.min_account_size:,.2f} for live trading (PDT rule)")
    
    # Validate gap parameters
    if config.trading.min_gap_percent >= config.trading.max_gap_percent:
        raise ValueError("min_gap_percent must be less than max_gap_percent")
    
    # Validate price range
    if config.trading.min_price >= config.trading.max_price:
        raise ValueError("min_price must be less than max_price")
    
    # Check API keys for live trading
    if not config.account.paper_trading:
        if not config.api.alpha_vantage_key and config.data.primary_provider == "alpha_vantage":
            raise ValueError("Alpha Vantage API key required for live trading")
    
    # Validate risk parameters
    if config.trading.risk_per_trade_percent <= 0 or config.trading.risk_per_trade_percent > 10:
        raise ValueError("risk_per_trade_percent must be between 0 and 10")
    
    if config.trading.daily_loss_limit_percent <= 0 or config.trading.daily_loss_limit_percent > 20:
        raise ValueError("daily_loss_limit_percent must be between 0 and 20")


def save_config(config: Config, config_path: str) -> None:
    """
    Save configuration to YAML file.
    
    Args:
        config: Configuration object to save
        config_path: Path to save configuration file
    """
    # Convert config to dictionary
    config_dict = {
        "api": {
            "alpha_vantage_key": config.api.alpha_vantage_key,
            "polygon_key": config.api.polygon_key,
            "iex_token": config.api.iex_token,
            "broker_api_key": config.api.broker_api_key,
            "broker_secret": config.api.broker_secret,
            "broker_base_url": config.api.broker_base_url,
        },
        "trading": {
            "min_gap_percent": config.trading.min_gap_percent,
            "max_gap_percent": config.trading.max_gap_percent,
            "min_price": config.trading.min_price,
            "max_price": config.trading.max_price,
            "min_volume_multiplier": config.trading.min_volume_multiplier,
            "min_float_shares": config.trading.min_float_shares,
            "ema_period": config.trading.ema_period,
            "rsi_period": config.trading.rsi_period,
            "rsi_min": config.trading.rsi_min,
            "rsi_max": config.trading.rsi_max,
            "volume_surge_multiplier": config.trading.volume_surge_multiplier,
            "market_open_delay_minutes": config.trading.market_open_delay_minutes,
            "risk_per_trade_percent": config.trading.risk_per_trade_percent,
            "max_positions": config.trading.max_positions,
            "daily_loss_limit_percent": config.trading.daily_loss_limit_percent,
            "stop_loss_percent": config.trading.stop_loss_percent,
            "profit_target_ratio": config.trading.profit_target_ratio,
            "trailing_stop_atr_multiplier": config.trading.trailing_stop_atr_multiplier,
            "market_close_time": config.trading.market_close_time,
            "no_trade_days": config.trading.no_trade_days,
        },
        "account": {
            "account_size": config.account.account_size,
            "broker": config.account.broker,
            "paper_trading": config.account.paper_trading,
            "min_account_size": config.account.min_account_size,
        },
        "data": {
            "primary_provider": config.data.primary_provider,
            "backup_providers": config.data.backup_providers,
            "update_interval_seconds": config.data.update_interval_seconds,
            "historical_days": config.data.historical_days,
            "cache_enabled": config.data.cache_enabled,
            "cache_duration_hours": config.data.cache_duration_hours,
        },
        "logging": {
            "level": config.logging.level,
            "log_dir": config.logging.log_dir,
            "max_file_size": config.logging.max_file_size,
            "backup_count": config.logging.backup_count,
            "format": config.logging.format,
        },
    }
    
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(config_path), exist_ok=True)
    
    # Save to YAML file
    with open(config_path, 'w') as f:
        yaml.dump(config_dict, f, default_flow_style=False, indent=2)
