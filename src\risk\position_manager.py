#!/usr/bin/env python3
"""
Position management system for MomentumTsunami trading bot.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import json

from utils.logger import setup_logger


@dataclass
class Position:
    """Position data structure."""
    symbol: str
    entry_price: float
    quantity: int
    stop_loss: float
    profit_target: float
    entry_time: datetime
    strategy: str
    position_id: str
    
    # Optional fields
    current_price: float = 0.0
    unrealized_pnl: float = 0.0
    trailing_stop: Optional[float] = None
    gap_fill_level: Optional[float] = None
    risk_amount: float = 0.0
    position_value: float = 0.0
    
    # Performance tracking
    max_favorable_excursion: float = 0.0
    max_adverse_excursion: float = 0.0
    
    # Status
    status: str = "OPEN"  # OPEN, CLOSED, PENDING
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert position to dictionary."""
        data = asdict(self)
        # Convert datetime to ISO string
        if isinstance(data['entry_time'], datetime):
            data['entry_time'] = data['entry_time'].isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Position':
        """Create position from dictionary."""
        # Convert ISO string back to datetime
        if isinstance(data['entry_time'], str):
            data['entry_time'] = datetime.fromisoformat(data['entry_time'])
        return cls(**data)


class PositionManager:
    """
    Manages trading positions with persistence and tracking.
    """
    
    def __init__(self, config):
        self.config = config
        self.logger = setup_logger("PositionManager", config.logging)
        
        # Position storage
        self.positions: Dict[str, Position] = {}
        self.closed_positions: List[Position] = []
        
        # Position tracking
        self.position_counter = 0
        self.daily_position_count = 0
        
        self.logger.info("Position Manager initialized")
    
    def create_position(self, symbol: str, entry_price: float, quantity: int,
                       stop_loss: float, profit_target: float, strategy: str = "gap_momentum",
                       gap_fill_level: Optional[float] = None) -> Position:
        """
        Create a new position.
        
        Args:
            symbol: Stock symbol
            entry_price: Entry price
            quantity: Position quantity
            stop_loss: Stop loss price
            profit_target: Profit target price
            strategy: Trading strategy name
            gap_fill_level: Gap fill level (for gap strategies)
            
        Returns:
            Created position object
        """
        self.position_counter += 1
        self.daily_position_count += 1
        
        position_id = f"{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{self.position_counter}"
        
        position = Position(
            symbol=symbol,
            entry_price=entry_price,
            quantity=quantity,
            stop_loss=stop_loss,
            profit_target=profit_target,
            entry_time=datetime.now(),
            strategy=strategy,
            position_id=position_id,
            gap_fill_level=gap_fill_level,
            risk_amount=(entry_price - stop_loss) * quantity,
            position_value=entry_price * quantity,
            current_price=entry_price
        )
        
        self.positions[symbol] = position
        
        self.logger.info(f"Position created: {position_id} - {symbol} {quantity} shares @ ${entry_price:.2f}")
        
        return position
    
    def update_position(self, symbol: str, current_price: float) -> Optional[Position]:
        """
        Update position with current market price.
        
        Args:
            symbol: Stock symbol
            current_price: Current market price
            
        Returns:
            Updated position or None if not found
        """
        if symbol not in self.positions:
            return None
        
        position = self.positions[symbol]
        position.current_price = current_price
        
        # Calculate unrealized P&L
        position.unrealized_pnl = (current_price - position.entry_price) * position.quantity
        
        # Update excursion tracking
        if position.unrealized_pnl > position.max_favorable_excursion:
            position.max_favorable_excursion = position.unrealized_pnl
        
        if position.unrealized_pnl < position.max_adverse_excursion:
            position.max_adverse_excursion = position.unrealized_pnl
        
        return position
    
    def close_position(self, symbol: str, exit_price: float, exit_reason: str = "manual") -> Optional[Position]:
        """
        Close a position.
        
        Args:
            symbol: Stock symbol
            exit_price: Exit price
            exit_reason: Reason for exit
            
        Returns:
            Closed position or None if not found
        """
        if symbol not in self.positions:
            return None
        
        position = self.positions[symbol]
        position.status = "CLOSED"
        position.current_price = exit_price
        
        # Calculate final P&L
        position.unrealized_pnl = (exit_price - position.entry_price) * position.quantity
        
        # Move to closed positions
        self.closed_positions.append(position)
        del self.positions[symbol]
        
        self.logger.info(f"Position closed: {position.position_id} @ ${exit_price:.2f} - "
                        f"P&L: ${position.unrealized_pnl:.2f} ({exit_reason})")
        
        return position
    
    def update_stop_loss(self, symbol: str, new_stop_loss: float) -> bool:
        """
        Update stop loss for a position.
        
        Args:
            symbol: Stock symbol
            new_stop_loss: New stop loss price
            
        Returns:
            True if updated successfully
        """
        if symbol not in self.positions:
            return False
        
        position = self.positions[symbol]
        old_stop = position.stop_loss
        position.stop_loss = new_stop_loss
        
        # Update risk amount
        position.risk_amount = (position.entry_price - new_stop_loss) * position.quantity
        
        self.logger.info(f"Stop loss updated for {symbol}: ${old_stop:.2f} -> ${new_stop_loss:.2f}")
        
        return True
    
    def update_trailing_stop(self, symbol: str, trailing_stop: float) -> bool:
        """
        Update trailing stop for a position.
        
        Args:
            symbol: Stock symbol
            trailing_stop: New trailing stop price
            
        Returns:
            True if updated successfully
        """
        if symbol not in self.positions:
            return False
        
        position = self.positions[symbol]
        
        # Only update if new trailing stop is higher than current stop
        if trailing_stop > position.stop_loss:
            old_stop = position.stop_loss
            position.stop_loss = trailing_stop
            position.trailing_stop = trailing_stop
            
            self.logger.info(f"Trailing stop updated for {symbol}: ${old_stop:.2f} -> ${trailing_stop:.2f}")
            return True
        
        return False
    
    def get_position(self, symbol: str) -> Optional[Position]:
        """Get position by symbol."""
        return self.positions.get(symbol)
    
    def get_all_positions(self) -> Dict[str, Position]:
        """Get all open positions."""
        return self.positions.copy()
    
    def get_positions_list(self) -> List[Position]:
        """Get all open positions as list."""
        return list(self.positions.values())
    
    def get_position_count(self) -> int:
        """Get number of open positions."""
        return len(self.positions)
    
    def get_total_position_value(self) -> float:
        """Get total value of all positions."""
        return sum(pos.position_value for pos in self.positions.values())
    
    def get_total_unrealized_pnl(self) -> float:
        """Get total unrealized P&L across all positions."""
        return sum(pos.unrealized_pnl for pos in self.positions.values())
    
    def get_total_risk_amount(self) -> float:
        """Get total risk amount across all positions."""
        return sum(pos.risk_amount for pos in self.positions.values())
    
    def get_positions_by_strategy(self, strategy: str) -> List[Position]:
        """Get positions filtered by strategy."""
        return [pos for pos in self.positions.values() if pos.strategy == strategy]
    
    def get_positions_summary(self) -> Dict[str, Any]:
        """Get summary of all positions."""
        if not self.positions:
            return {
                'position_count': 0,
                'total_value': 0.0,
                'total_unrealized_pnl': 0.0,
                'total_risk': 0.0,
                'positions': []
            }
        
        positions_data = []
        for pos in self.positions.values():
            positions_data.append({
                'symbol': pos.symbol,
                'quantity': pos.quantity,
                'entry_price': pos.entry_price,
                'current_price': pos.current_price,
                'unrealized_pnl': pos.unrealized_pnl,
                'unrealized_pnl_percent': (pos.unrealized_pnl / pos.position_value) * 100 if pos.position_value > 0 else 0,
                'stop_loss': pos.stop_loss,
                'profit_target': pos.profit_target,
                'strategy': pos.strategy,
                'hold_time': str(datetime.now() - pos.entry_time)
            })
        
        return {
            'position_count': len(self.positions),
            'total_value': self.get_total_position_value(),
            'total_unrealized_pnl': self.get_total_unrealized_pnl(),
            'total_risk': self.get_total_risk_amount(),
            'positions': positions_data
        }
    
    def check_stop_losses(self) -> List[Dict[str, Any]]:
        """
        Check all positions for stop loss breaches.
        
        Returns:
            List of positions that hit stop loss
        """
        stop_loss_alerts = []
        
        for symbol, position in self.positions.items():
            if position.current_price <= position.stop_loss:
                alert = {
                    'symbol': symbol,
                    'current_price': position.current_price,
                    'stop_loss': position.stop_loss,
                    'unrealized_pnl': position.unrealized_pnl,
                    'position_id': position.position_id,
                    'alert_type': 'STOP_LOSS_BREACH'
                }
                stop_loss_alerts.append(alert)
        
        return stop_loss_alerts
    
    def check_profit_targets(self) -> List[Dict[str, Any]]:
        """
        Check all positions for profit target hits.
        
        Returns:
            List of positions that hit profit target
        """
        profit_target_alerts = []
        
        for symbol, position in self.positions.items():
            if position.current_price >= position.profit_target:
                alert = {
                    'symbol': symbol,
                    'current_price': position.current_price,
                    'profit_target': position.profit_target,
                    'unrealized_pnl': position.unrealized_pnl,
                    'position_id': position.position_id,
                    'alert_type': 'PROFIT_TARGET_HIT'
                }
                profit_target_alerts.append(alert)
        
        return profit_target_alerts
    
    def get_daily_stats(self) -> Dict[str, Any]:
        """Get daily position statistics."""
        closed_today = [pos for pos in self.closed_positions 
                       if pos.entry_time.date() == datetime.now().date()]
        
        if not closed_today:
            return {
                'positions_opened': self.daily_position_count,
                'positions_closed': 0,
                'realized_pnl': 0.0,
                'win_rate': 0.0,
                'avg_hold_time': timedelta(0)
            }
        
        winning_positions = [pos for pos in closed_today if pos.unrealized_pnl > 0]
        total_pnl = sum(pos.unrealized_pnl for pos in closed_today)
        win_rate = len(winning_positions) / len(closed_today) * 100
        
        # Calculate average hold time
        hold_times = [datetime.now() - pos.entry_time for pos in closed_today]
        avg_hold_time = sum(hold_times, timedelta(0)) / len(hold_times)
        
        return {
            'positions_opened': self.daily_position_count,
            'positions_closed': len(closed_today),
            'realized_pnl': total_pnl,
            'win_rate': win_rate,
            'avg_hold_time': avg_hold_time,
            'winning_positions': len(winning_positions),
            'losing_positions': len(closed_today) - len(winning_positions)
        }
    
    def save_positions_to_file(self, filepath: str) -> bool:
        """Save positions to JSON file."""
        try:
            data = {
                'timestamp': datetime.now().isoformat(),
                'open_positions': [pos.to_dict() for pos in self.positions.values()],
                'closed_positions': [pos.to_dict() for pos in self.closed_positions[-100:]]  # Last 100 closed
            }
            
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            
            self.logger.info(f"Positions saved to {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving positions to file: {e}")
            return False
    
    def load_positions_from_file(self, filepath: str) -> bool:
        """Load positions from JSON file."""
        try:
            with open(filepath, 'r') as f:
                data = json.load(f)
            
            # Load open positions
            for pos_data in data.get('open_positions', []):
                position = Position.from_dict(pos_data)
                self.positions[position.symbol] = position
            
            # Load recent closed positions
            for pos_data in data.get('closed_positions', []):
                position = Position.from_dict(pos_data)
                self.closed_positions.append(position)
            
            self.logger.info(f"Positions loaded from {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error loading positions from file: {e}")
            return False
    
    def reset_daily_counters(self):
        """Reset daily counters for new trading day."""
        self.daily_position_count = 0
        self.logger.info("Daily position counters reset")
