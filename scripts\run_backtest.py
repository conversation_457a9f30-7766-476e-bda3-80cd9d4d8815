#!/usr/bin/env python3
"""
Backtesting script for MomentumTsunami trading bot.
"""

import asyncio
import argparse
import sys
from pathlib import Path
from datetime import datetime, timedelta

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from config import load_config
from analytics.backtester import Backtester


async def main():
    parser = argparse.ArgumentParser(description="MomentumTsunami Backtesting")
    parser.add_argument("--start-date", type=str, required=True,
                       help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end-date", type=str, required=True,
                       help="End date (YYYY-MM-DD)")
    parser.add_argument("--config", type=str, default="config/settings.yaml",
                       help="Configuration file path")
    parser.add_argument("--optimize", action="store_true",
                       help="Run parameter optimization")
    parser.add_argument("--output", type=str, default="backtest_results.json",
                       help="Output file for results")
    
    args = parser.parse_args()
    
    # Load configuration
    config = load_config(args.config)
    config.mode = "backtest"
    
    # Create backtester
    backtester = Backtester(config)
    
    if args.optimize:
        print("Running parameter optimization...")
        
        # Define parameter ranges to test
        param_ranges = {
            'risk_per_trade_percent': [0.5, 1.0, 1.5, 2.0],
            'min_gap_percent': [2.0, 3.0, 4.0],
            'max_gap_percent': [6.0, 8.0, 10.0],
            'profit_target_ratio': [1.0, 1.5, 2.0, 2.5]
        }
        
        results = await backtester.optimize_parameters(
            args.start_date, args.end_date, param_ranges
        )
        
        print(f"\nOptimization Results:")
        print(f"Best Parameters: {results['best_parameters']}")
        print(f"Best Sharpe Ratio: {results['best_score']:.3f}")
        print(f"Combinations Tested: {results['total_combinations_tested']}")
        
    else:
        print(f"Running backtest from {args.start_date} to {args.end_date}...")
        
        results = await backtester.run_single_backtest(args.start_date, args.end_date)
        
        if 'error' in results:
            print(f"Backtest failed: {results['error']}")
            return
        
        # Display results
        stats = results['statistics']
        print(f"\nBacktest Results:")
        print(f"Initial Capital: ${results['initial_capital']:,.2f}")
        print(f"Final Equity: ${results['final_equity']:,.2f}")
        print(f"Total Return: {stats['total_return_percent']:.2f}%")
        print(f"Total Trades: {results['total_trades']}")
        print(f"Win Rate: {stats['win_rate']:.1f}%")
        print(f"Profit Factor: {stats['profit_factor']:.2f}")
        print(f"Sharpe Ratio: {stats['sharpe_ratio']:.2f}")
        print(f"Max Drawdown: ${stats['max_drawdown']:,.2f}")
    
    # Save results to file
    import json
    with open(args.output, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\nResults saved to {args.output}")


if __name__ == "__main__":
    asyncio.run(main())
