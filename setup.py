#!/usr/bin/env python3
"""
Setup script for MomentumTsunami - Advanced Gap-Up Day Trading Bot
"""

from setuptools import setup, find_packages
import os

# Read the README file
with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

# Read requirements
with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="momentum-tsunami",
    version="1.0.0",
    author="Hector Ta",
    author_email="<EMAIL>",
    description="Advanced Gap-Up Day Trading Bot for Momentum Strategies",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/HectorTa1989/MomentumTsunami",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business :: Financial :: Investment",
        "Topic :: Scientific/Engineering :: Information Analysis",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.20.0",
            "pytest-mock>=3.8.0",
            "black>=22.0.0",
            "flake8>=5.0.0",
            "mypy>=0.991",
        ],
        "brokers": [
            "alpaca-trade-api>=3.0.0",
            "ib-insync>=0.9.70",
            "robin-stocks>=2.1.0",
        ],
        "visualization": [
            "matplotlib>=3.5.0",
            "plotly>=5.10.0",
            "dash>=2.6.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "momentum-tsunami=main:main",
            "mt-backtest=analytics.backtester:main",
            "mt-scanner=data.gap_scanner:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.yaml", "*.yml", "*.json", "*.txt"],
    },
    zip_safe=False,
    keywords="trading, day-trading, momentum, gap-up, algorithmic-trading, python, finance",
    project_urls={
        "Bug Reports": "https://github.com/HectorTa1989/MomentumTsunami/issues",
        "Source": "https://github.com/HectorTa1989/MomentumTsunami",
        "Documentation": "https://github.com/HectorTa1989/MomentumTsunami/docs",
    },
)
