#!/usr/bin/env python3
"""
Risk management system for MomentumTsunami trading bot.
"""

from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import pandas as pd
from dataclasses import dataclass

from utils.logger import setup_logger
from utils.helpers import calculate_position_size, safe_divide


@dataclass
class RiskMetrics:
    """Risk metrics for a trading session."""
    daily_pnl: float = 0.0
    daily_loss_limit: float = 0.0
    max_positions: int = 3
    current_positions: int = 0
    account_size: float = 25000.0
    buying_power_used: float = 0.0
    max_risk_per_trade: float = 1.0
    total_risk_exposure: float = 0.0


class RiskManager:
    """
    Comprehensive risk management system for day trading.
    
    Features:
    - Position sizing based on risk percentage
    - Daily loss limits
    - Maximum position limits
    - Buying power management
    - Risk exposure monitoring
    - Circuit breaker functionality
    """
    
    def __init__(self, config):
        self.config = config
        self.logger = setup_logger("RiskManager", config.logging)
        
        # Risk parameters
        self.account_size = config.account.account_size
        self.max_positions = config.trading.max_positions
        self.daily_loss_limit_percent = config.trading.daily_loss_limit_percent
        self.risk_per_trade_percent = config.trading.risk_per_trade_percent
        self.stop_loss_percent = config.trading.stop_loss_percent
        
        # Risk state
        self.daily_pnl = 0.0
        self.positions = {}
        self.daily_trades = []
        self.risk_breaches = []
        self.trading_halted = False
        self.halt_reason = None
        
        # Calculate limits
        self.daily_loss_limit = self.account_size * (self.daily_loss_limit_percent / 100)
        self.max_risk_per_trade = self.account_size * (self.risk_per_trade_percent / 100)
        
        self.logger.info(f"Risk Manager initialized - Account: ${self.account_size:,.2f}, "
                        f"Daily Loss Limit: ${self.daily_loss_limit:,.2f}, "
                        f"Max Risk/Trade: ${self.max_risk_per_trade:,.2f}")
    
    def calculate_position_size(self, account_size: float, risk_percent: float, 
                              entry_price: float, stop_loss: float) -> int:
        """
        Calculate position size based on risk management rules.
        
        Args:
            account_size: Total account size
            risk_percent: Risk percentage per trade
            entry_price: Entry price per share
            stop_loss: Stop loss price per share
            
        Returns:
            Number of shares to buy
        """
        return calculate_position_size(account_size, risk_percent, entry_price, stop_loss)
    
    def validate_new_position(self, symbol: str, entry_price: float, 
                            stop_loss: float, quantity: int) -> Dict[str, Any]:
        """
        Validate if a new position can be opened based on risk rules.
        
        Args:
            symbol: Stock symbol
            entry_price: Proposed entry price
            stop_loss: Proposed stop loss
            quantity: Proposed quantity
            
        Returns:
            Validation result dictionary
        """
        validation = {
            'approved': False,
            'reason': '',
            'adjusted_quantity': quantity,
            'risk_amount': 0.0,
            'position_value': 0.0
        }
        
        # Check if trading is halted
        if self.trading_halted:
            validation['reason'] = f"Trading halted: {self.halt_reason}"
            return validation
        
        # Check maximum positions limit
        if len(self.positions) >= self.max_positions:
            validation['reason'] = f"Maximum positions limit reached ({self.max_positions})"
            return validation
        
        # Check if already have position in this symbol
        if symbol in self.positions:
            validation['reason'] = f"Already have position in {symbol}"
            return validation
        
        # Calculate position risk
        risk_per_share = entry_price - stop_loss
        if risk_per_share <= 0:
            validation['reason'] = "Invalid stop loss - must be below entry price"
            return validation
        
        position_value = entry_price * quantity
        risk_amount = risk_per_share * quantity
        
        # Check individual trade risk limit
        if risk_amount > self.max_risk_per_trade:
            # Adjust quantity to meet risk limit
            max_quantity = int(self.max_risk_per_trade / risk_per_share)
            if max_quantity > 0:
                validation['adjusted_quantity'] = max_quantity
                validation['risk_amount'] = risk_per_share * max_quantity
                validation['position_value'] = entry_price * max_quantity
                self.logger.warning(f"Position size adjusted for {symbol}: {quantity} -> {max_quantity} shares")
            else:
                validation['reason'] = f"Risk per share too high - exceeds maximum risk per trade"
                return validation
        else:
            validation['risk_amount'] = risk_amount
            validation['position_value'] = position_value
        
        # Check buying power (use 25% of account max per position)
        max_position_value = self.account_size * 0.25
        if validation['position_value'] > max_position_value:
            max_quantity_bp = int(max_position_value / entry_price)
            if max_quantity_bp < validation['adjusted_quantity']:
                validation['adjusted_quantity'] = max_quantity_bp
                validation['position_value'] = entry_price * max_quantity_bp
                validation['risk_amount'] = risk_per_share * max_quantity_bp
                self.logger.warning(f"Position size adjusted for buying power {symbol}: {max_quantity_bp} shares")
        
        # Check total risk exposure
        current_risk_exposure = sum(pos.get('risk_amount', 0) for pos in self.positions.values())
        total_risk_exposure = current_risk_exposure + validation['risk_amount']
        max_total_risk = self.account_size * 0.05  # Max 5% total risk exposure
        
        if total_risk_exposure > max_total_risk:
            validation['reason'] = f"Total risk exposure would exceed limit (${total_risk_exposure:,.2f} > ${max_total_risk:,.2f})"
            return validation
        
        # Check daily loss limit
        if abs(self.daily_pnl) >= self.daily_loss_limit:
            validation['reason'] = f"Daily loss limit reached (${abs(self.daily_pnl):,.2f})"
            return validation
        
        # All checks passed
        validation['approved'] = True
        validation['reason'] = "Position approved"
        
        return validation
    
    def add_position(self, symbol: str, entry_price: float, quantity: int, 
                    stop_loss: float, entry_time: datetime = None) -> bool:
        """
        Add a new position to risk tracking.
        
        Args:
            symbol: Stock symbol
            entry_price: Entry price
            quantity: Position quantity
            stop_loss: Stop loss price
            entry_time: Entry timestamp
            
        Returns:
            True if position added successfully
        """
        if entry_time is None:
            entry_time = datetime.now()
        
        risk_per_share = entry_price - stop_loss
        risk_amount = risk_per_share * quantity
        position_value = entry_price * quantity
        
        position = {
            'symbol': symbol,
            'entry_price': entry_price,
            'quantity': quantity,
            'stop_loss': stop_loss,
            'entry_time': entry_time,
            'risk_amount': risk_amount,
            'position_value': position_value,
            'unrealized_pnl': 0.0,
            'max_favorable_excursion': 0.0,
            'max_adverse_excursion': 0.0
        }
        
        self.positions[symbol] = position
        
        self.logger.info(f"Position added: {symbol} - {quantity} shares @ ${entry_price:.2f} "
                        f"(Risk: ${risk_amount:.2f})")
        
        return True
    
    def update_position(self, symbol: str, current_price: float) -> Optional[Dict[str, Any]]:
        """
        Update position with current market price.
        
        Args:
            symbol: Stock symbol
            current_price: Current market price
            
        Returns:
            Updated position data or None if position not found
        """
        if symbol not in self.positions:
            return None
        
        position = self.positions[symbol]
        entry_price = position['entry_price']
        quantity = position['quantity']
        
        # Calculate unrealized P&L
        unrealized_pnl = (current_price - entry_price) * quantity
        position['unrealized_pnl'] = unrealized_pnl
        
        # Update excursion tracking
        if unrealized_pnl > position['max_favorable_excursion']:
            position['max_favorable_excursion'] = unrealized_pnl
        
        if unrealized_pnl < position['max_adverse_excursion']:
            position['max_adverse_excursion'] = unrealized_pnl
        
        # Check for stop loss breach
        if current_price <= position['stop_loss']:
            self.logger.warning(f"Stop loss breached for {symbol}: ${current_price:.2f} <= ${position['stop_loss']:.2f}")
            return self._create_stop_loss_alert(position, current_price)
        
        return position
    
    def close_position(self, symbol: str, exit_price: float, exit_time: datetime = None) -> Optional[Dict[str, Any]]:
        """
        Close a position and update P&L tracking.
        
        Args:
            symbol: Stock symbol
            exit_price: Exit price
            exit_time: Exit timestamp
            
        Returns:
            Closed position data or None if position not found
        """
        if symbol not in self.positions:
            return None
        
        if exit_time is None:
            exit_time = datetime.now()
        
        position = self.positions[symbol]
        entry_price = position['entry_price']
        quantity = position['quantity']
        
        # Calculate realized P&L
        realized_pnl = (exit_price - entry_price) * quantity
        
        # Update daily P&L
        self.daily_pnl += realized_pnl
        
        # Create trade record
        trade_record = {
            'symbol': symbol,
            'entry_price': entry_price,
            'exit_price': exit_price,
            'quantity': quantity,
            'realized_pnl': realized_pnl,
            'entry_time': position['entry_time'],
            'exit_time': exit_time,
            'hold_time': exit_time - position['entry_time'],
            'max_favorable_excursion': position['max_favorable_excursion'],
            'max_adverse_excursion': position['max_adverse_excursion'],
            'risk_amount': position['risk_amount']
        }
        
        self.daily_trades.append(trade_record)
        
        # Remove position
        del self.positions[symbol]
        
        self.logger.info(f"Position closed: {symbol} @ ${exit_price:.2f} - P&L: ${realized_pnl:.2f}")
        
        # Check if daily loss limit breached
        if abs(self.daily_pnl) >= self.daily_loss_limit:
            self._trigger_circuit_breaker("Daily loss limit reached")
        
        return trade_record
    
    def _create_stop_loss_alert(self, position: Dict[str, Any], current_price: float) -> Dict[str, Any]:
        """Create stop loss alert."""
        return {
            'alert_type': 'STOP_LOSS_BREACH',
            'symbol': position['symbol'],
            'current_price': current_price,
            'stop_loss': position['stop_loss'],
            'unrealized_pnl': position['unrealized_pnl'],
            'timestamp': datetime.now().isoformat()
        }
    
    def _trigger_circuit_breaker(self, reason: str):
        """Trigger circuit breaker to halt trading."""
        self.trading_halted = True
        self.halt_reason = reason
        
        breach_record = {
            'timestamp': datetime.now().isoformat(),
            'reason': reason,
            'daily_pnl': self.daily_pnl,
            'positions_count': len(self.positions),
            'trades_count': len(self.daily_trades)
        }
        
        self.risk_breaches.append(breach_record)
        
        self.logger.error(f"CIRCUIT BREAKER TRIGGERED: {reason}")
        self.logger.error(f"Daily P&L: ${self.daily_pnl:.2f}, Positions: {len(self.positions)}")
    
    def get_risk_metrics(self) -> RiskMetrics:
        """Get current risk metrics."""
        current_risk_exposure = sum(pos.get('risk_amount', 0) for pos in self.positions.values())
        buying_power_used = sum(pos.get('position_value', 0) for pos in self.positions.values())
        
        return RiskMetrics(
            daily_pnl=self.daily_pnl,
            daily_loss_limit=self.daily_loss_limit,
            max_positions=self.max_positions,
            current_positions=len(self.positions),
            account_size=self.account_size,
            buying_power_used=buying_power_used,
            max_risk_per_trade=self.max_risk_per_trade,
            total_risk_exposure=current_risk_exposure
        )
    
    def get_position_summary(self) -> Dict[str, Any]:
        """Get summary of all positions."""
        total_unrealized_pnl = sum(pos.get('unrealized_pnl', 0) for pos in self.positions.values())
        total_position_value = sum(pos.get('position_value', 0) for pos in self.positions.values())
        total_risk_amount = sum(pos.get('risk_amount', 0) for pos in self.positions.values())
        
        return {
            'position_count': len(self.positions),
            'total_position_value': total_position_value,
            'total_unrealized_pnl': total_unrealized_pnl,
            'total_risk_amount': total_risk_amount,
            'daily_realized_pnl': self.daily_pnl,
            'total_daily_pnl': self.daily_pnl + total_unrealized_pnl,
            'positions': list(self.positions.keys())
        }
    
    def get_daily_stats(self) -> Dict[str, Any]:
        """Get daily trading statistics."""
        if not self.daily_trades:
            return {
                'trades_count': 0,
                'win_rate': 0.0,
                'avg_win': 0.0,
                'avg_loss': 0.0,
                'largest_win': 0.0,
                'largest_loss': 0.0,
                'total_pnl': self.daily_pnl
            }
        
        winning_trades = [t for t in self.daily_trades if t['realized_pnl'] > 0]
        losing_trades = [t for t in self.daily_trades if t['realized_pnl'] < 0]
        
        win_rate = len(winning_trades) / len(self.daily_trades) * 100
        avg_win = sum(t['realized_pnl'] for t in winning_trades) / len(winning_trades) if winning_trades else 0
        avg_loss = sum(t['realized_pnl'] for t in losing_trades) / len(losing_trades) if losing_trades else 0
        largest_win = max((t['realized_pnl'] for t in winning_trades), default=0)
        largest_loss = min((t['realized_pnl'] for t in losing_trades), default=0)
        
        return {
            'trades_count': len(self.daily_trades),
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'largest_win': largest_win,
            'largest_loss': largest_loss,
            'total_pnl': self.daily_pnl,
            'profit_factor': safe_divide(abs(sum(t['realized_pnl'] for t in winning_trades)), 
                                       abs(sum(t['realized_pnl'] for t in losing_trades)), 0)
        }
    
    def reset_daily_tracking(self):
        """Reset daily tracking for new trading day."""
        self.daily_pnl = 0.0
        self.daily_trades.clear()
        self.trading_halted = False
        self.halt_reason = None
        
        # Keep positions but reset daily metrics
        for position in self.positions.values():
            position['max_favorable_excursion'] = 0.0
            position['max_adverse_excursion'] = 0.0
        
        self.logger.info("Daily risk tracking reset for new trading day")
    
    def is_trading_allowed(self) -> Tuple[bool, str]:
        """
        Check if trading is currently allowed.
        
        Returns:
            Tuple of (allowed, reason)
        """
        if self.trading_halted:
            return False, self.halt_reason
        
        if abs(self.daily_pnl) >= self.daily_loss_limit:
            return False, "Daily loss limit reached"
        
        if len(self.positions) >= self.max_positions:
            return False, "Maximum positions limit reached"
        
        return True, "Trading allowed"
