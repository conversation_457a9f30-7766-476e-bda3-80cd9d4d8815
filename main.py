#!/usr/bin/env python3
"""
MomentumTsunami - Advanced Gap-Up Day Trading Bot
Main entry point for the trading bot.

Author: <PERSON>: <EMAIL>
"""

import asyncio
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.main import main

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)
