#!/usr/bin/env python3
"""
Gap scanner for MomentumTsunami trading bot.
Identifies stocks gapping up based on specified criteria.
"""

import asyncio
from typing import Dict, List, Optional, Any
import pandas as pd
from datetime import datetime, timedelta
import yfinance as yf

from utils.logger import setup_logger
from utils.helpers import calculate_gap_percentage, is_pre_market, retry_async


class GapScanner:
    """
    Scanner for identifying gap-up stocks based on momentum criteria.
    """
    
    def __init__(self, config, data_feed):
        self.config = config
        self.data_feed = data_feed
        self.logger = setup_logger("GapScanner", config.logging)
        
        # Scanner configuration
        self.min_gap = config.trading.min_gap_percent
        self.max_gap = config.trading.max_gap_percent
        self.min_price = config.trading.min_price
        self.max_price = config.trading.max_price
        self.min_volume_multiplier = config.trading.min_volume_multiplier
        self.min_float_shares = config.trading.min_float_shares
        
        # Stock universe for scanning
        self.stock_universe = []
        self.last_scan_time = None
        
        self.logger.info(f"Gap scanner initialized - Gap range: {self.min_gap}%-{self.max_gap}%")
    
    async def initialize(self):
        """Initialize the gap scanner."""
        await self._load_stock_universe()
        self.logger.info(f"Loaded {len(self.stock_universe)} stocks for scanning")
    
    async def _load_stock_universe(self):
        """Load universe of stocks to scan."""
        # For demo purposes, using a predefined list of liquid stocks
        # In production, this would come from a stock screener or database
        
        self.stock_universe = [
            # Tech stocks
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX',
            'ADBE', 'CRM', 'ORCL', 'INTC', 'AMD', 'QCOM', 'AVGO', 'TXN',
            
            # Healthcare/Biotech
            'JNJ', 'PFE', 'UNH', 'ABBV', 'TMO', 'DHR', 'ABT', 'LLY',
            'GILD', 'AMGN', 'BIIB', 'REGN', 'VRTX', 'ILMN', 'MRNA',
            
            # Financial
            'JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'AXP', 'BLK',
            'SCHW', 'USB', 'PNC', 'TFC', 'COF', 'CME', 'ICE',
            
            # Consumer
            'AMZN', 'HD', 'MCD', 'NKE', 'SBUX', 'TGT', 'WMT', 'COST',
            'LOW', 'TJX', 'DIS', 'CMCSA', 'VZ', 'T', 'NFLX',
            
            # Industrial
            'BA', 'CAT', 'GE', 'MMM', 'HON', 'UPS', 'RTX', 'LMT',
            'NOC', 'GD', 'FDX', 'UNP', 'CSX', 'NSC', 'DAL',
            
            # Energy
            'XOM', 'CVX', 'COP', 'EOG', 'SLB', 'PSX', 'VLO', 'MPC',
            'KMI', 'OKE', 'WMB', 'EPD', 'ET', 'MPLX', 'PAA',
            
            # Materials
            'LIN', 'APD', 'ECL', 'SHW', 'DD', 'DOW', 'PPG', 'NEM',
            'FCX', 'GOLD', 'AA', 'X', 'CLF', 'NUE', 'STLD',
            
            # Utilities
            'NEE', 'DUK', 'SO', 'D', 'EXC', 'XEL', 'SRE', 'PEG',
            'ED', 'EIX', 'WEC', 'ES', 'DTE', 'PPL', 'CMS',
            
            # REITs
            'AMT', 'PLD', 'CCI', 'EQIX', 'PSA', 'WELL', 'SPG', 'O',
            'SBAC', 'DLR', 'EXR', 'AVB', 'EQR', 'VTR', 'ARE'
        ]
        
        # Remove duplicates and sort
        self.stock_universe = sorted(list(set(self.stock_universe)))
    
    async def scan_gap_ups(self) -> List[Dict[str, Any]]:
        """
        Scan for stocks gapping up based on criteria.
        
        Returns:
            List of stocks meeting gap-up criteria
        """
        if not is_pre_market() and not self.config.debug:
            self.logger.warning("Gap scanning should be performed during pre-market hours")
        
        self.logger.info(f"Starting gap scan of {len(self.stock_universe)} stocks...")
        
        gap_stocks = []
        batch_size = 20  # Process stocks in batches to avoid rate limits
        
        for i in range(0, len(self.stock_universe), batch_size):
            batch = self.stock_universe[i:i + batch_size]
            batch_results = await self._scan_batch(batch)
            gap_stocks.extend(batch_results)
            
            # Small delay between batches to respect rate limits
            await asyncio.sleep(1)
        
        self.last_scan_time = datetime.now()
        
        self.logger.info(f"Gap scan complete. Found {len(gap_stocks)} potential gap-up stocks")
        
        return gap_stocks
    
    async def _scan_batch(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """Scan a batch of symbols for gap-ups."""
        tasks = [self._check_gap_up(symbol) for symbol in symbols]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        gap_stocks = []
        for symbol, result in zip(symbols, results):
            if isinstance(result, Exception):
                self.logger.debug(f"Error scanning {symbol}: {result}")
            elif result:
                gap_stocks.append(result)
        
        return gap_stocks
    
    @retry_async(max_retries=2, delay=0.5)
    async def _check_gap_up(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Check if a single stock meets gap-up criteria.
        
        Args:
            symbol: Stock symbol to check
            
        Returns:
            Stock data if it meets criteria, None otherwise
        """
        try:
            # Get current quote
            quote = await self.data_feed.get_real_time_data(symbol)
            
            if not quote or 'price' not in quote:
                return None
            
            current_price = quote['price']
            previous_close = quote.get('previous_close', 0)
            volume = quote.get('volume', 0)
            
            if previous_close == 0:
                return None
            
            # Calculate gap percentage
            gap_percent = calculate_gap_percentage(current_price, previous_close)
            
            # Check basic gap criteria
            if not (self.min_gap <= gap_percent <= self.max_gap):
                return None
            
            # Check price range
            if not (self.min_price <= current_price <= self.max_price):
                return None
            
            # Get additional stock information
            stock_info = await self._get_stock_info(symbol)
            
            if not stock_info:
                return None
            
            # Check volume criteria
            avg_volume = stock_info.get('avg_volume', 0)
            if avg_volume > 0:
                volume_ratio = volume / avg_volume
                if volume_ratio < self.min_volume_multiplier:
                    return None
            
            # Check float shares
            float_shares = stock_info.get('float_shares', 0)
            if float_shares > 0 and float_shares < self.min_float_shares:
                return None
            
            # Compile gap stock data
            gap_stock = {
                'symbol': symbol,
                'current_price': current_price,
                'previous_close': previous_close,
                'gap_percent': gap_percent,
                'gap_amount': current_price - previous_close,
                'volume': volume,
                'avg_volume': avg_volume,
                'volume_ratio': volume / avg_volume if avg_volume > 0 else 0,
                'market_cap': stock_info.get('market_cap', 0),
                'float_shares': float_shares,
                'sector': stock_info.get('sector', ''),
                'industry': stock_info.get('industry', ''),
                'scan_time': datetime.now().isoformat(),
                'gap_fill_level': previous_close  # Level where gap would be filled
            }
            
            self.logger.info(f"Gap-up found: {symbol} @ ${current_price:.2f} ({gap_percent:.1f}% gap)")
            
            return gap_stock
            
        except Exception as e:
            self.logger.debug(f"Error checking gap for {symbol}: {e}")
            return None
    
    async def _get_stock_info(self, symbol: str) -> Dict[str, Any]:
        """Get additional stock information."""
        try:
            # Use yfinance for fundamental data (free alternative)
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            return {
                'market_cap': info.get('marketCap', 0),
                'float_shares': info.get('floatShares', info.get('sharesOutstanding', 0)),
                'avg_volume': info.get('averageVolume', info.get('averageVolume10days', 0)),
                'sector': info.get('sector', ''),
                'industry': info.get('industry', ''),
                'beta': info.get('beta', 0),
                'pe_ratio': info.get('trailingPE', 0),
                'price_to_book': info.get('priceToBook', 0)
            }
            
        except Exception as e:
            self.logger.debug(f"Error getting stock info for {symbol}: {e}")
            return {}
    
    async def filter_stocks(self, gap_stocks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Apply additional filters to gap-up stocks.
        
        Args:
            gap_stocks: List of gap-up stocks
            
        Returns:
            Filtered list of stocks
        """
        if not gap_stocks:
            return []
        
        self.logger.info(f"Applying additional filters to {len(gap_stocks)} gap-up stocks...")
        
        filtered_stocks = []
        
        for stock in gap_stocks:
            # Check for news catalysts (placeholder - would integrate with news API)
            has_catalyst = await self._check_news_catalyst(stock['symbol'])
            stock['has_news_catalyst'] = has_catalyst
            
            # Calculate gap quality score
            gap_score = self._calculate_gap_score(stock)
            stock['gap_score'] = gap_score
            
            # Apply minimum score threshold
            if gap_score >= 0.6:  # Minimum 60% score
                filtered_stocks.append(stock)
        
        # Sort by gap score (best first)
        filtered_stocks.sort(key=lambda x: x['gap_score'], reverse=True)
        
        # Limit to top candidates
        max_candidates = 20
        filtered_stocks = filtered_stocks[:max_candidates]
        
        self.logger.info(f"Filter complete. {len(filtered_stocks)} stocks passed all criteria")
        
        return filtered_stocks
    
    def _calculate_gap_score(self, stock: Dict[str, Any]) -> float:
        """
        Calculate a quality score for the gap-up stock.
        
        Args:
            stock: Stock data dictionary
            
        Returns:
            Score between 0 and 1
        """
        score = 0.0
        
        # Gap size score (prefer 4-6% gaps)
        gap_percent = stock['gap_percent']
        if 4 <= gap_percent <= 6:
            score += 0.3
        elif 3 <= gap_percent <= 7:
            score += 0.2
        else:
            score += 0.1
        
        # Volume score
        volume_ratio = stock.get('volume_ratio', 0)
        if volume_ratio >= 3:
            score += 0.3
        elif volume_ratio >= 2:
            score += 0.2
        else:
            score += 0.1
        
        # Price range score (prefer mid-range prices)
        price = stock['current_price']
        if 20 <= price <= 100:
            score += 0.2
        elif 10 <= price <= 200:
            score += 0.1
        
        # Market cap score (prefer mid to large cap)
        market_cap = stock.get('market_cap', 0)
        if market_cap >= 10_000_000_000:  # $10B+
            score += 0.1
        elif market_cap >= 2_000_000_000:  # $2B+
            score += 0.05
        
        # News catalyst bonus
        if stock.get('has_news_catalyst', False):
            score += 0.1
        
        return min(score, 1.0)
    
    async def _check_news_catalyst(self, symbol: str) -> bool:
        """
        Check for news catalysts (placeholder implementation).
        
        Args:
            symbol: Stock symbol
            
        Returns:
            True if news catalyst detected
        """
        # Placeholder - in production would integrate with news APIs
        # like Alpha Vantage News, Polygon News, or financial news services
        
        # For now, return False (no news catalyst detection)
        return False
    
    def get_scan_summary(self) -> Dict[str, Any]:
        """Get summary of last scan."""
        return {
            'last_scan_time': self.last_scan_time.isoformat() if self.last_scan_time else None,
            'universe_size': len(self.stock_universe),
            'scan_criteria': {
                'gap_range': f"{self.min_gap}%-{self.max_gap}%",
                'price_range': f"${self.min_price}-${self.max_price}",
                'min_volume_multiplier': self.min_volume_multiplier,
                'min_float_shares': self.min_float_shares
            }
        }


if __name__ == "__main__":
    """Standalone gap scanner for testing."""
    import sys
    from pathlib import Path
    
    # Add parent directories to path
    sys.path.append(str(Path(__file__).parent.parent.parent))
    
    from config import load_config
    from data.data_feed import DataFeed
    
    async def main():
        config = load_config()
        config.debug = True
        
        data_feed = DataFeed(config)
        await data_feed.initialize()
        
        scanner = GapScanner(config, data_feed)
        await scanner.initialize()
        
        # Run gap scan
        gap_stocks = await scanner.scan_gap_ups()
        filtered_stocks = await scanner.filter_stocks(gap_stocks)
        
        print(f"\nFound {len(filtered_stocks)} gap-up stocks:")
        for stock in filtered_stocks:
            print(f"{stock['symbol']}: ${stock['current_price']:.2f} ({stock['gap_percent']:.1f}% gap, score: {stock['gap_score']:.2f})")
        
        await data_feed.cleanup()
    
    asyncio.run(main())
